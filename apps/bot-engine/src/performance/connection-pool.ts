/**
 * Connection Pool - Manages connection pooling for external services
 * Optimizes HTTP connections to GitHub, Vercel, Twilio, and other APIs
 */

import { EventEmitter } from 'events';
import { Agent } from 'https';
import { request, RequestOptions } from 'https';
import { URL } from 'url';

/**
 * Connection pool configuration
 */
export interface ConnectionPoolConfig {
  // Pool settings
  maxConnections: number;
  maxConnectionsPerHost: number;
  keepAlive: boolean;
  keepAliveMsecs: number;
  timeout: number;
  
  // Retry settings
  retryAttempts: number;
  retryDelay: number;
  retryBackoff: number;
  
  // Service-specific settings
  services: {
    github: ServiceConfig;
    vercel: ServiceConfig;
    twilio: ServiceConfig;
    gemini: ServiceConfig;
  };
}

/**
 * Service-specific configuration
 */
export interface ServiceConfig {
  baseUrl: string;
  maxConnections: number;
  timeout: number;
  retryAttempts: number;
  headers: Record<string, string>;
  rateLimit?: {
    requests: number;
    window: number; // milliseconds
  };
}

/**
 * Connection pool statistics
 */
export interface ConnectionPoolStats {
  total: {
    active: number;
    idle: number;
    created: number;
    destroyed: number;
    requests: number;
    errors: number;
  };
  services: Record<string, {
    active: number;
    idle: number;
    queueSize: number;
    utilization: number;
  }>;
}

/**
 * Request options for pooled requests
 */
export interface PooledRequestOptions extends RequestOptions {
  service: string;
  retryAttempts?: number;
  timeout?: number;
  body?: string | Buffer;
}

/**
 * Connection pool events
 */
export interface ConnectionPoolEvents {
  'connection:created': (service: string, host: string) => void;
  'connection:destroyed': (service: string, host: string) => void;
  'connection:reused': (service: string, host: string) => void;
  'request:queued': (service: string, queueSize: number) => void;
  'request:completed': (service: string, duration: number) => void;
  'request:failed': (service: string, error: Error) => void;
  'pool:full': (service: string) => void;
  'rate:limited': (service: string) => void;
}

/**
 * Rate limiter for API requests
 */
class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number, windowMs: number) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  canMakeRequest(): boolean {
    const now = Date.now();
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    return this.requests.length < this.maxRequests;
  }

  recordRequest(): void {
    this.requests.push(Date.now());
  }

  getNextAvailableTime(): number {
    if (this.requests.length === 0) {
      return 0;
    }
    
    const oldestRequest = Math.min(...this.requests);
    return oldestRequest + this.windowMs - Date.now();
  }
}

/**
 * Service connection pool
 */
class ServicePool {
  private agent: Agent;
  private rateLimiter?: RateLimiter;
  private activeConnections: number = 0;
  private totalRequests: number = 0;
  private totalErrors: number = 0;
  private requestQueue: Array<() => void> = [];
  private config: ServiceConfig;

  constructor(public readonly name: string, config: ServiceConfig) {
    this.config = config;
    
    // Create HTTP agent with connection pooling
    this.agent = new Agent({
      keepAlive: true,
      keepAliveMsecs: 30000,
      maxSockets: config.maxConnections,
      maxFreeSockets: Math.floor(config.maxConnections / 2),
      timeout: config.timeout,
      scheduling: 'fifo'
    });
    
    // Setup rate limiter if configured
    if (config.rateLimit) {
      this.rateLimiter = new RateLimiter(
        config.rateLimit.requests,
        config.rateLimit.window
      );
    }
  }

  /**
   * Make a request using the pool
   */
  async makeRequest(options: PooledRequestOptions): Promise<any> {
    return new Promise((resolve, reject) => {
      const executeRequest = async () => {
        try {
          // Check rate limit
          if (this.rateLimiter && !this.rateLimiter.canMakeRequest()) {
            const waitTime = this.rateLimiter.getNextAvailableTime();
            if (waitTime > 0) {
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }
          }

          // Record request
          this.totalRequests++;
          this.activeConnections++;
          
          if (this.rateLimiter) {
            this.rateLimiter.recordRequest();
          }

          // Make the actual request
          const result = await this.executeHttpRequest(options);
          
          this.activeConnections--;
          resolve(result);
          
        } catch (error) {
          this.activeConnections--;
          this.totalErrors++;
          reject(error);
        }
      };

      // Queue request if pool is full
      if (this.activeConnections >= this.config.maxConnections) {
        this.requestQueue.push(executeRequest);
      } else {
        executeRequest();
      }
    });
  }

  /**
   * Execute HTTP request
   */
  private async executeHttpRequest(options: PooledRequestOptions): Promise<any> {
    return new Promise((resolve, reject) => {
      const url = new URL(options.path || '/', this.config.baseUrl);
      const requestOptions: RequestOptions = {
        ...options,
        hostname: url.hostname,
        port: url.port,
        path: url.pathname + url.search,
        agent: this.agent,
        headers: {
          ...this.config.headers,
          ...options.headers
        }
      };

      const req = request(requestOptions, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const result = {
              statusCode: res.statusCode,
              headers: res.headers,
              data: data
            };
            
            if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
              resolve(result);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${data}`));
            }
          } catch (error) {
            reject(error);
          }
          
          // Process next request in queue
          this.processQueue();
        });
      });

      req.on('error', (error) => {
        reject(error);
        this.processQueue();
      });

      req.setTimeout(options.timeout || this.config.timeout, () => {
        req.destroy();
        reject(new Error('Request timeout'));
        this.processQueue();
      });

      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  }

  /**
   * Process queued requests
   */
  private processQueue(): void {
    if (this.requestQueue.length > 0 && this.activeConnections < this.config.maxConnections) {
      const nextRequest = this.requestQueue.shift();
      if (nextRequest) {
        nextRequest();
      }
    }
  }

  /**
   * Get pool statistics
   */
  getStats(): ConnectionPoolStats['services'][string] {
    return {
      active: this.activeConnections,
      idle: this.agent.freeSockets ? Object.keys(this.agent.freeSockets).length : 0,
      queueSize: this.requestQueue.length,
      utilization: (this.activeConnections / this.config.maxConnections) * 100
    };
  }

  /**
   * Destroy the pool
   */
  destroy(): void {
    this.agent.destroy();
    this.requestQueue = [];
    this.activeConnections = 0;
  }
}

export declare interface ConnectionPool {
  on<K extends keyof ConnectionPoolEvents>(event: K, listener: ConnectionPoolEvents[K]): this;
  emit<K extends keyof ConnectionPoolEvents>(event: K, ...args: Parameters<ConnectionPoolEvents[K]>): boolean;
}

/**
 * Connection Pool Manager
 */
export class ConnectionPool extends EventEmitter {
  private config: ConnectionPoolConfig;
  private pools: Map<string, ServicePool> = new Map();
  private totalConnections: number = 0;
  private totalRequests: number = 0;
  private totalErrors: number = 0;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(config: ConnectionPoolConfig) {
    super();
    this.config = config;
    this.initializePools();
    this.startCleanupInterval();
  }

  /**
   * Initialize service pools
   */
  private initializePools(): void {
    for (const [serviceName, serviceConfig] of Object.entries(this.config.services)) {
      const pool = new ServicePool(serviceName, serviceConfig);
      this.pools.set(serviceName, pool);
      console.log(`[ConnectionPool] Initialized pool for ${serviceName}`);
    }
  }

  /**
   * Make a request using the appropriate pool
   */
  async makeRequest(options: PooledRequestOptions): Promise<any> {
    const pool = this.pools.get(options.service);
    if (!pool) {
      throw new Error(`No pool found for service: ${options.service}`);
    }

    const startTime = Date.now();
    
    try {
      // Apply retry logic
      const retryAttempts = options.retryAttempts || this.config.retryAttempts;
      let lastError: Error | null = null;
      
      for (let attempt = 0; attempt <= retryAttempts; attempt++) {
        try {
          this.emit('request:queued', options.service, pool.getStats().queueSize);
          
          const result = await pool.makeRequest(options);
          
          const duration = Date.now() - startTime;
          this.emit('request:completed', options.service, duration);
          this.totalRequests++;
          
          return result;
          
        } catch (error) {
          lastError = error as Error;
          
          if (attempt < retryAttempts) {
            const delay = this.config.retryDelay * Math.pow(this.config.retryBackoff, attempt);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }
      
      this.totalErrors++;
      this.emit('request:failed', options.service, lastError!);
      throw lastError;
      
    } catch (error) {
      this.totalErrors++;
      this.emit('request:failed', options.service, error as Error);
      throw error;
    }
  }

  /**
   * Make GitHub API request
   */
  async makeGitHubRequest(path: string, options: Partial<PooledRequestOptions> = {}): Promise<any> {
    return this.makeRequest({
      ...options,
      service: 'github',
      path,
      method: options.method || 'GET'
    });
  }

  /**
   * Make Vercel API request
   */
  async makeVercelRequest(path: string, options: Partial<PooledRequestOptions> = {}): Promise<any> {
    return this.makeRequest({
      ...options,
      service: 'vercel',
      path,
      method: options.method || 'GET'
    });
  }

  /**
   * Make Twilio API request
   */
  async makeTwilioRequest(path: string, options: Partial<PooledRequestOptions> = {}): Promise<any> {
    return this.makeRequest({
      ...options,
      service: 'twilio',
      path,
      method: options.method || 'POST'
    });
  }

  /**
   * Make Gemini API request
   */
  async makeGeminiRequest(path: string, options: Partial<PooledRequestOptions> = {}): Promise<any> {
    return this.makeRequest({
      ...options,
      service: 'gemini',
      path,
      method: options.method || 'POST'
    });
  }

  /**
   * Get connection pool statistics
   */
  getStats(): ConnectionPoolStats {
    const services: Record<string, ConnectionPoolStats['services'][string]> = {};
    
    for (const [name, pool] of this.pools) {
      services[name] = pool.getStats();
    }

    return {
      total: {
        active: Array.from(this.pools.values()).reduce((sum, pool) => sum + pool.getStats().active, 0),
        idle: Array.from(this.pools.values()).reduce((sum, pool) => sum + pool.getStats().idle, 0),
        created: this.totalConnections,
        destroyed: 0, // Would need to track this
        requests: this.totalRequests,
        errors: this.totalErrors
      },
      services
    };
  }

  /**
   * Get pool health
   */
  getHealth(): Record<string, any> {
    const health: Record<string, any> = {};
    
    for (const [name, pool] of this.pools) {
      const stats = pool.getStats();
      health[name] = {
        healthy: stats.utilization < 90,
        utilization: stats.utilization,
        activeConnections: stats.active,
        queueSize: stats.queueSize,
        status: stats.utilization > 95 ? 'critical' : 
                stats.utilization > 80 ? 'warning' : 'healthy'
      };
    }
    
    return health;
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Every minute
  }

  /**
   * Cleanup idle connections
   */
  private cleanup(): void {
    const stats = this.getStats();
    
    for (const [serviceName, serviceStats] of Object.entries(stats.services)) {
      // Log utilization
      if (serviceStats.utilization > 80) {
        console.warn(`[ConnectionPool] High utilization for ${serviceName}: ${serviceStats.utilization}%`);
      }
      
      // Emit events for monitoring
      if (serviceStats.utilization > 95) {
        this.emit('pool:full', serviceName);
      }
    }
  }

  /**
   * Destroy all pools
   */
  async destroy(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    for (const pool of this.pools.values()) {
      pool.destroy();
    }
    
    this.pools.clear();
    console.log('[ConnectionPool] Destroyed all connection pools');
  }
}

/**
 * Create connection pool instance
 */
export function createConnectionPool(config: ConnectionPoolConfig): ConnectionPool {
  return new ConnectionPool(config);
}

/**
 * Default connection pool configuration
 */
export const DEFAULT_CONNECTION_POOL_CONFIG: ConnectionPoolConfig = {
  maxConnections: 100,
  maxConnectionsPerHost: 10,
  keepAlive: true,
  keepAliveMsecs: 30000,
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
  retryBackoff: 2,
  services: {
    github: {
      baseUrl: 'https://api.github.com',
      maxConnections: 20,
      timeout: 30000,
      retryAttempts: 3,
      headers: {
        'User-Agent': 'WhatsApp-Website-Bot/1.0',
        'Accept': 'application/vnd.github.v3+json'
      },
      rateLimit: {
        requests: 5000,
        window: 3600000 // 1 hour
      }
    },
    vercel: {
      baseUrl: 'https://api.vercel.com',
      maxConnections: 15,
      timeout: 30000,
      retryAttempts: 3,
      headers: {
        'User-Agent': 'WhatsApp-Website-Bot/1.0',
        'Content-Type': 'application/json'
      },
      rateLimit: {
        requests: 100,
        window: 60000 // 1 minute
      }
    },
    twilio: {
      baseUrl: 'https://api.twilio.com',
      maxConnections: 10,
      timeout: 15000,
      retryAttempts: 2,
      headers: {
        'User-Agent': 'WhatsApp-Website-Bot/1.0',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      rateLimit: {
        requests: 1000,
        window: 60000 // 1 minute
      }
    },
    gemini: {
      baseUrl: 'https://generativelanguage.googleapis.com',
      maxConnections: 5,
      timeout: 60000,
      retryAttempts: 2,
      headers: {
        'User-Agent': 'WhatsApp-Website-Bot/1.0',
        'Content-Type': 'application/json'
      },
      rateLimit: {
        requests: 60,
        window: 60000 // 1 minute
      }
    }
  }
};