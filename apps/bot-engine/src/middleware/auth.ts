/**
 * Authentication middleware for webhook endpoints
 * Provides API key authentication and request validation
 */

import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';

export interface AuthenticatedRequest extends Request {
  isAuthenticated?: boolean;
  apiKeySource?: string;
}

/**
 * Authentication configuration
 */
export interface AuthConfig {
  /** API key for webhook authentication */
  apiKey?: string;
  /** Secret key for signature verification */
  secretKey?: string;
  /** Enable signature verification */
  requireSignature?: boolean;
  /** Enable rate limiting */
  enableRateLimit?: boolean;
}

/**
 * Default authentication configuration
 */
const DEFAULT_AUTH_CONFIG: AuthConfig = {
  apiKey: process.env.FLY_IO_API_KEY || process.env.WEBHOOK_API_KEY,
  secretKey: process.env.WEBHOOK_SECRET_KEY,
  requireSignature: false,
  enableRateLimit: true
};

/**
 * API Key authentication middleware
 * Validates incoming webhook requests using API key
 */
export function apiKeyAuth(config: AuthConfig = DEFAULT_AUTH_CONFIG) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // Skip authentication if no API key is configured
      if (!config.apiKey) {
        console.warn('[Auth] No API key configured, skipping authentication');
        req.isAuthenticated = true;
        req.apiKeySource = 'none';
        return next();
      }

      // Extract API key from headers
      const authHeader = req.headers.authorization;
      const apiKeyHeader = req.headers['x-api-key'] as string;
      
      let providedApiKey: string | undefined;
      let source: string = 'unknown';

      // Check Authorization header (Bearer token)
      if (authHeader && authHeader.startsWith('Bearer ')) {
        providedApiKey = authHeader.substring(7);
        source = 'bearer';
      }
      // Check X-API-Key header
      else if (apiKeyHeader) {
        providedApiKey = apiKeyHeader;
        source = 'header';
      }
      // Check query parameter (less secure, for testing)
      else if (req.query.api_key) {
        providedApiKey = req.query.api_key as string;
        source = 'query';
      }

      // Validate API key
      if (!providedApiKey) {
        console.warn('[Auth] No API key provided in request');
        return res.status(401).json({
          error: 'Authentication required',
          message: 'API key must be provided via Authorization header, X-API-Key header, or api_key query parameter'
        });
      }

      // Compare API keys securely
      if (!secureCompare(providedApiKey, config.apiKey)) {
        console.warn(`[Auth] Invalid API key provided via ${source}`);
        return res.status(401).json({
          error: 'Invalid API key',
          message: 'The provided API key is invalid'
        });
      }

      // Authentication successful
      req.isAuthenticated = true;
      req.apiKeySource = source;
      
      console.log(`[Auth] Request authenticated via ${source}`);
      next();
    } catch (error) {
      console.error('[Auth] Authentication error:', error);
      res.status(500).json({
        error: 'Authentication error',
        message: 'Internal server error during authentication'
      });
    }
  };
}

/**
 * Webhook signature verification middleware
 * Validates webhook signatures to ensure authenticity
 */
export function signatureAuth(config: AuthConfig = DEFAULT_AUTH_CONFIG) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // Skip signature verification if not required or no secret key
      if (!config.requireSignature || !config.secretKey) {
        return next();
      }

      // Extract signature from headers
      const signature = req.headers['x-webhook-signature'] as string;
      const timestampHeader = req.headers['x-webhook-timestamp'] as string;
      
      if (!signature) {
        console.warn('[Auth] No webhook signature provided');
        return res.status(401).json({
          error: 'Signature required',
          message: 'Webhook signature must be provided via X-Webhook-Signature header'
        });
      }

      // Verify timestamp (optional, prevents replay attacks)
      if (timestampHeader) {
        const timestamp = parseInt(timestampHeader);
        const currentTime = Math.floor(Date.now() / 1000);
        const timeDiff = Math.abs(currentTime - timestamp);
        
        // Allow 5 minute tolerance
        if (timeDiff > 300) {
          console.warn('[Auth] Webhook timestamp too old or in future');
          return res.status(401).json({
            error: 'Invalid timestamp',
            message: 'Webhook timestamp is too old or in the future'
          });
        }
      }

      // Create expected signature
      const bodyBuffer = Buffer.from(JSON.stringify(req.body));
      const expectedSignature = crypto
        .createHmac('sha256', config.secretKey)
        .update(bodyBuffer)
        .digest('hex');

      // Compare signatures
      if (!secureCompare(signature, `sha256=${expectedSignature}`)) {
        console.warn('[Auth] Invalid webhook signature');
        return res.status(401).json({
          error: 'Invalid signature',
          message: 'Webhook signature verification failed'
        });
      }

      console.log('[Auth] Webhook signature verified successfully');
      next();
    } catch (error) {
      console.error('[Auth] Signature verification error:', error);
      res.status(500).json({
        error: 'Signature verification error',
        message: 'Internal server error during signature verification'
      });
    }
  };
}

/**
 * Request validation middleware
 * Validates request format and required fields
 */
export function validateWebhookRequest() {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Check Content-Type
      if (!req.is('application/json')) {
        res.status(400).json({
          error: 'Invalid content type',
          message: 'Content-Type must be application/json'
        });
        return;
      }

      // Check for required fields in webhook payload
      if (!req.body) {
        res.status(400).json({
          error: 'Missing body',
          message: 'Request body is required'
        });
        return;
      }

      // Validate webhook event structure
      const { event } = req.body;
      if (!event) {
        res.status(400).json({
          error: 'Missing event',
          message: 'Webhook event is required'
        });
        return;
      }

      // Validate event structure
      if (!event.id || !event.type || !event.timestamp || !event.data) {
        res.status(400).json({
          error: 'Invalid event structure',
          message: 'Event must contain id, type, timestamp, and data fields'
        });
        return;
      }

      console.log(`[Auth] Webhook request validated - Event: ${event.type}`);
      next();
    } catch (error) {
      console.error('[Auth] Request validation error:', error);
      res.status(500).json({
        error: 'Validation error',
        message: 'Internal server error during request validation'
      });
    }
  };
}

/**
 * Security headers middleware
 * Adds security headers to responses
 */
export function securityHeaders() {
  return (_req: Request, res: Response, next: NextFunction): void => {
    // Basic security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // CORS headers (basic)
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, X-Webhook-Signature, X-Webhook-Timestamp');
    
    next();
  };
}

/**
 * Request logging middleware
 * Logs incoming webhook requests
 */
export function requestLogger() {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // Log request
    console.log(`[Request] ${req.method} ${req.path} from ${req.ip}`);
    
    // Log response when finished
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      console.log(`[Response] ${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
    });
    
    next();
  };
}

/**
 * Secure string comparison to prevent timing attacks
 */
function secureCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

/**
 * Extract client IP address from request
 */
export function getClientIP(req: Request): string {
  const forwarded = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  
  if (forwarded && typeof forwarded === 'string') {
    const firstForwarded = forwarded.split(',')[0];
    return firstForwarded ? firstForwarded.trim() : '';
  }
  
  if (realIP && typeof realIP === 'string') {
    return realIP;
  }
  
  return req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress || 'unknown';
}

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  windowMs: number;
  max: number;
  message: string;
}

/**
 * Create rate limiting middleware
 */
export function createRateLimit(config: RateLimitConfig) {
  // Simple in-memory rate limiting
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction): void => {
    const clientIP = getClientIP(req);
    const now = Date.now();
    
    // Clean up expired entries
    for (const [ip, data] of requests.entries()) {
      if (now > data.resetTime) {
        requests.delete(ip);
      }
    }
    
    // Get or create request data
    let requestData = requests.get(clientIP);
    if (!requestData || now > requestData.resetTime) {
      requestData = { count: 0, resetTime: now + config.windowMs };
      requests.set(clientIP, requestData);
    }
    
    // Check rate limit
    if (requestData.count >= config.max) {
      res.status(429).json({
        error: 'Rate limit exceeded',
        message: config.message,
        retryAfter: Math.ceil((requestData.resetTime - now) / 1000)
      });
      return;
    }
    
    // Increment counter
    requestData.count++;
    
    // Add rate limit headers
    res.setHeader('X-RateLimit-Limit', config.max.toString());
    res.setHeader('X-RateLimit-Remaining', (config.max - requestData.count).toString());
    res.setHeader('X-RateLimit-Reset', Math.ceil(requestData.resetTime / 1000).toString());
    
    next();
  };
}