/**
 * SessionManager implementation for WhatsApp Website Bot
 * Handles session lifecycle, persistence, and conversation state management
 */

import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { existsSync } from 'fs';
// Local type definitions to avoid import issues
type SessionState = 'created' | 'active' | 'completed' | 'failed' | 'timeout';
type SessionOperation = 'create' | 'edit' | 'import' | 'scaffold';

interface User {
  phoneNumber: string;
  name?: string;
  profilePicture?: string;
  language?: string;
  registeredAt?: Date;
  lastActiveAt?: Date;
  isActive?: boolean;
  preferences?: any;
  metadata?: Record<string, any>;
}

interface SessionMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

interface SessionContext {
  messages: SessionMessage[];
  intent?: string;
  project?: any;
  preferences?: any;
  data?: Record<string, any>;
}

interface NarrativeItem {
  type: 'text' | 'tool_call' | 'file_operation' | 'deployment' | 'error';
  content?: string;
  tool?: string;
  file?: string;
  operation?: 'create' | 'update' | 'delete' | 'read';
  timestamp: number;
  metadata?: Record<string, any>;
}

interface StreamingState {
  active: boolean;
  messageId?: string;
  content?: string;
  narrative: NarrativeItem[];
  synthesis?: string;
}

export interface Workspace {
  id: string;
  path: string;
  isGitWorktree: boolean;
  basePath?: string;
  branch?: string;
  historyPath?: string;
  type: 'temporary' | 'persistent' | 'template' | 'import';
  user: User;
  project?: any;
  createdAt: Date;
  lastAccessedAt: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

interface SessionOptions {
  id?: string;
  user: User;
  operation: SessionOperation;
  workspace: Workspace;
  context?: SessionContext;
  timeout?: number;
  process?: any;
  startedAt?: Date;
  endedAt?: Date | null;
  exitCode?: number | null;
  error?: string | null;
  lastResponse?: string;
  streaming?: StreamingState;
}

// WebsiteSession class definition
export class WebsiteSession {
  public readonly id: string;
  public readonly user: User;
  public readonly operation: SessionOperation;
  public readonly workspace: Workspace;
  public state: SessionState;
  public context: SessionContext;
  public readonly timeoutMs: number;
  public process: any;
  public readonly startedAt: Date;
  public endedAt: Date | null;
  public exitCode: number | null;
  public error: string | null;
  public lastResponse: string;
  public streaming: StreamingState;

  constructor(options: SessionOptions) {
    this.id = options.id || this.generateSessionId();
    this.user = options.user;
    this.operation = options.operation;
    this.workspace = options.workspace;
    this.state = 'created';
    this.context = options.context || { messages: [] };
    this.timeoutMs = options.timeout || 30 * 60 * 1000; // 30 minutes default
    this.process = options.process || null;
    this.startedAt = options.startedAt || new Date();
    this.endedAt = options.endedAt || null;
    this.exitCode = options.exitCode || null;
    this.error = options.error || null;
    this.lastResponse = options.lastResponse || '';
    this.streaming = options.streaming || {
      active: false,
      narrative: [],
    };
  }

  private generateSessionId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  isActive(): boolean {
    return this.state === 'active' && this.process !== null;
  }

  isCompleted(): boolean {
    return this.state === 'completed' && this.exitCode === 0;
  }

  hasFailed(): boolean {
    return this.state === 'failed' || (this.exitCode !== null && this.exitCode !== 0);
  }

  hasTimedOut(): boolean {
    return this.state === 'timeout' ||
           (this.startedAt && Date.now() - this.startedAt.getTime() > this.timeoutMs);
  }

  start(): void {
    if (this.state !== 'created') {
      throw new Error(`Cannot start session in state: ${this.state}`);
    }
    this.state = 'active';
  }

  complete(): void {
    if (this.state !== 'active') {
      throw new Error(`Cannot complete session in state: ${this.state}`);
    }
    this.state = 'completed';
    this.endedAt = new Date();
    this.exitCode = 0;
  }

  fail(error: string): void {
    this.state = 'failed';
    this.endedAt = new Date();
    this.error = error;
    this.exitCode = 1;
  }

  timeout(): void {
    this.state = 'timeout';
    this.endedAt = new Date();
    this.error = 'Session timed out';
    this.exitCode = 124; // Standard timeout exit code
  }

  addMessage(message: Omit<SessionMessage, 'id' | 'timestamp'>): void {
    const sessionMessage: SessionMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };
    this.context.messages.push(sessionMessage);
  }

  addNarrativeItem(item: Omit<NarrativeItem, 'timestamp'>): void {
    const narrativeItem: NarrativeItem = {
      ...item,
      timestamp: Date.now(),
    };
    this.streaming.narrative.push(narrativeItem);
  }

  resetStreamingState(): void {
    this.streaming = {
      active: false,
      narrative: [],
    };
  }

  getSummary(): any {
    return {
      id: this.id,
      user: this.user,
      operation: this.operation,
      state: this.state,
      startedAt: this.startedAt,
      endedAt: this.endedAt,
      duration: this.endedAt ? this.endedAt.getTime() - this.startedAt.getTime() : null,
      exitCode: this.exitCode,
      error: this.error,
      messageCount: this.context.messages.length,
      projectName: this.context.project?.name,
    };
  }
}

interface ISessionManager {
  createSession(options: SessionOptions): Promise<WebsiteSession>;
  getSession(id: string): Promise<WebsiteSession | null>;
  getSessionsByUser(phoneNumber: string): Promise<WebsiteSession[]>;
  updateSession(session: WebsiteSession): Promise<void>;
  deleteSession(id: string): Promise<void>;
  getActiveSessions(): Promise<WebsiteSession[]>;
  cleanupExpiredSessions(): Promise<void>;
}

interface SessionPersistenceData {
  sessions: string[];
  lastUpdated: string;
}

interface SessionStorage {
  id: string;
  userId: string;
  operation: SessionOperation;
  state: SessionState;
  context: SessionContext;
  timeoutMs: number;
  startedAt: string;
  endedAt: string | null;
  exitCode: number | null;
  error: string | null;
  lastResponse: string;
  streaming: {
    active: boolean;
    messageId?: string;
    content?: string;
    narrative: NarrativeItem[];
    synthesis?: string;
  };
  workspace: Workspace;
  user: User;
}

export class SessionManager implements ISessionManager {
  private readonly sessionsDir: string;
  private readonly usersDir: string;
  private readonly activeDir: string;
  private readonly archiveDir: string;
  private readonly sessionCache: Map<string, WebsiteSession> = new Map();
  private readonly cleanupInterval: NodeJS.Timeout;
  private readonly maxCacheSize: number = 100;
  private readonly sessionTimeoutMs: number = 30 * 60 * 1000; // 30 minutes

  constructor(baseDir: string = '/app/sessions') {
    this.sessionsDir = baseDir;
    this.usersDir = join(baseDir, 'users');
    this.activeDir = join(baseDir, 'active');
    this.archiveDir = join(baseDir, 'archive');

    // Initialize directories
    this.initializeDirectories();

    // Start cleanup interval (every 5 minutes)
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions().catch(error => {
        console.error('Error during session cleanup:', error);
      });
    }, 5 * 60 * 1000);
  }

  /**
   * Initialize required directories
   */
  private async initializeDirectories(): Promise<void> {
    const directories = [this.sessionsDir, this.usersDir, this.activeDir, this.archiveDir];
    
    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        console.error(`Failed to create directory ${dir}:`, error);
        throw new Error(`Failed to initialize session storage: ${error}`);
      }
    }
  }

  /**
   * Create a new session
   */
  async createSession(options: SessionOptions): Promise<WebsiteSession> {
    try {
      const session = new WebsiteSession(options);
      
      // Store in cache
      this.sessionCache.set(session.id, session);
      
      // Persist to file system
      await this.persistSession(session);
      
      // Add to user's session list
      await this.addSessionToUser(session.user.phoneNumber, session.id);
      
      console.log(`[SessionManager] Created session ${session.id} for user ${session.user.phoneNumber}`);
      
      return session;
    } catch (error) {
      console.error(`[SessionManager] Failed to create session:`, error);
      throw new Error(`Failed to create session: ${error}`);
    }
  }

  /**
   * Get session by ID
   */
  async getSession(id: string): Promise<WebsiteSession | null> {
    try {
      // Check cache first
      if (this.sessionCache.has(id)) {
        return this.sessionCache.get(id)!;
      }

      // Load from file system
      const session = await this.loadSessionFromFile(id);
      if (session) {
        // Add to cache
        this.sessionCache.set(id, session);
        
        // Manage cache size
        if (this.sessionCache.size > this.maxCacheSize) {
          const oldestKey = this.sessionCache.keys().next().value;
          if (oldestKey) {
            this.sessionCache.delete(oldestKey);
          }
        }
      }

      return session;
    } catch (error) {
      console.error(`[SessionManager] Failed to get session ${id}:`, error);
      return null;
    }
  }

  /**
   * Get sessions by user phone number
   */
  async getSessionsByUser(phoneNumber: string): Promise<WebsiteSession[]> {
    try {
      const userSessionsPath = join(this.usersDir, phoneNumber, 'sessions.json');
      
      if (!existsSync(userSessionsPath)) {
        return [];
      }

      const userData = await fs.readFile(userSessionsPath, 'utf-8');
      const sessionData: SessionPersistenceData = JSON.parse(userData);
      
      const sessions: WebsiteSession[] = [];
      
      for (const sessionId of sessionData.sessions) {
        const session = await this.getSession(sessionId);
        if (session) {
          sessions.push(session);
        }
      }

      return sessions.sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime());
    } catch (error) {
      console.error(`[SessionManager] Failed to get sessions for user ${phoneNumber}:`, error);
      return [];
    }
  }

  /**
   * Update session
   */
  async updateSession(session: WebsiteSession): Promise<void> {
    try {
      // Update cache
      this.sessionCache.set(session.id, session);
      
      // Persist to file system
      await this.persistSession(session);
      
      console.log(`[SessionManager] Updated session ${session.id}`);
    } catch (error) {
      console.error(`[SessionManager] Failed to update session ${session.id}:`, error);
      throw new Error(`Failed to update session: ${error}`);
    }
  }

  /**
   * Delete session
   */
  async deleteSession(id: string): Promise<void> {
    try {
      // Remove from cache
      this.sessionCache.delete(id);
      
      // Get session to find user
      const session = await this.loadSessionFromFile(id);
      if (session) {
        // Remove from user's session list
        await this.removeSessionFromUser(session.user.phoneNumber, id);
      }
      
      // Delete from active directory
      const activeFile = join(this.activeDir, `${id}.json`);
      if (existsSync(activeFile)) {
        await fs.unlink(activeFile);
      }
      
      // Delete from archive directory
      const archiveFile = join(this.archiveDir, `${id}.json`);
      if (existsSync(archiveFile)) {
        await fs.unlink(archiveFile);
      }
      
      console.log(`[SessionManager] Deleted session ${id}`);
    } catch (error) {
      console.error(`[SessionManager] Failed to delete session ${id}:`, error);
      throw new Error(`Failed to delete session: ${error}`);
    }
  }

  /**
   * Get active sessions
   */
  async getActiveSessions(): Promise<WebsiteSession[]> {
    try {
      const activeFiles = await fs.readdir(this.activeDir);
      const sessions: WebsiteSession[] = [];
      
      for (const file of activeFiles) {
        if (file.endsWith('.json')) {
          const sessionId = file.replace('.json', '');
          const session = await this.getSession(sessionId);
          
          if (session && session.state === 'active') {
            sessions.push(session);
          }
        }
      }
      
      return sessions;
    } catch (error) {
      console.error(`[SessionManager] Failed to get active sessions:`, error);
      return [];
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = Date.now();
      const activeSessions = await this.getActiveSessions();
      let cleanedCount = 0;
      
      for (const session of activeSessions) {
        const sessionAge = now - session.startedAt.getTime();
        
        if (sessionAge > this.sessionTimeoutMs) {
          // Mark as timed out
          session.timeout();
          
          // Move to archive
          await this.archiveSession(session);
          
          // Remove from cache
          this.sessionCache.delete(session.id);
          
          cleanedCount++;
          
          console.log(`[SessionManager] Cleaned up expired session ${session.id}`);
        }
      }
      
      if (cleanedCount > 0) {
        console.log(`[SessionManager] Cleaned up ${cleanedCount} expired sessions`);
      }
    } catch (error) {
      console.error(`[SessionManager] Failed to cleanup expired sessions:`, error);
    }
  }

  /**
   * Get active session for user
   */
  async getActiveSession(userId: string): Promise<WebsiteSession | null> {
    try {
      const userSessions = await this.getSessionsByUser(userId);
      
      return userSessions.find(session => session.state === 'active') || null;
    } catch (error) {
      console.error(`[SessionManager] Failed to get active session for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Add message to session
   */
  async addMessage(sessionId: string, message: NarrativeItem): Promise<void> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      session.addNarrativeItem(message);
      await this.updateSession(session);
    } catch (error) {
      console.error(`[SessionManager] Failed to add message to session ${sessionId}:`, error);
      throw new Error(`Failed to add message: ${error}`);
    }
  }

  /**
   * Get session messages
   */
  async getSessionMessages(sessionId: string): Promise<NarrativeItem[]> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        return [];
      }
      
      return session.streaming.narrative;
    } catch (error) {
      console.error(`[SessionManager] Failed to get messages for session ${sessionId}:`, error);
      return [];
    }
  }

  /**
   * Persist session to file system
   */
  private async persistSession(session: WebsiteSession): Promise<void> {
    const sessionData: SessionStorage = {
      id: session.id,
      userId: session.user.phoneNumber,
      operation: session.operation,
      state: session.state,
      context: session.context,
      timeoutMs: session.timeoutMs,
      startedAt: session.startedAt.toISOString(),
      endedAt: session.endedAt?.toISOString() || null,
      exitCode: session.exitCode,
      error: session.error,
      lastResponse: session.lastResponse,
      streaming: session.streaming,
      workspace: session.workspace,
      user: session.user
    };

    const targetDir = session.state === 'active' ? this.activeDir : this.archiveDir;
    const filePath = join(targetDir, `${session.id}.json`);
    
    // Ensure parent directory exists
    await fs.mkdir(dirname(filePath), { recursive: true });
    
    // Write session data
    await fs.writeFile(filePath, JSON.stringify(sessionData, null, 2), 'utf-8');
    
    // If moving to archive, remove from active
    if (session.state !== 'active') {
      const activeFile = join(this.activeDir, `${session.id}.json`);
      if (existsSync(activeFile)) {
        await fs.unlink(activeFile);
      }
    }
  }

  /**
   * Load session from file system
   */
  private async loadSessionFromFile(sessionId: string): Promise<WebsiteSession | null> {
    const possiblePaths = [
      join(this.activeDir, `${sessionId}.json`),
      join(this.archiveDir, `${sessionId}.json`)
    ];

    for (const filePath of possiblePaths) {
      if (existsSync(filePath)) {
        try {
          const data = await fs.readFile(filePath, 'utf-8');
          const sessionData: SessionStorage = JSON.parse(data);
          
          return new WebsiteSession({
            id: sessionData.id,
            user: sessionData.user,
            operation: sessionData.operation,
            workspace: sessionData.workspace,
            context: sessionData.context,
            timeout: sessionData.timeoutMs,
            startedAt: new Date(sessionData.startedAt),
            endedAt: sessionData.endedAt ? new Date(sessionData.endedAt) : null,
            exitCode: sessionData.exitCode,
            error: sessionData.error,
            lastResponse: sessionData.lastResponse,
            streaming: sessionData.streaming
          });
        } catch (error) {
          console.error(`[SessionManager] Failed to load session from ${filePath}:`, error);
        }
      }
    }

    return null;
  }

  /**
   * Add session to user's session list
   */
  private async addSessionToUser(phoneNumber: string, sessionId: string): Promise<void> {
    const userDir = join(this.usersDir, phoneNumber);
    const sessionsFile = join(userDir, 'sessions.json');
    
    // Ensure user directory exists
    await fs.mkdir(userDir, { recursive: true });
    
    let sessionData: SessionPersistenceData;
    
    if (existsSync(sessionsFile)) {
      const data = await fs.readFile(sessionsFile, 'utf-8');
      sessionData = JSON.parse(data);
    } else {
      sessionData = { sessions: [], lastUpdated: new Date().toISOString() };
    }
    
    // Add session if not already present
    if (!sessionData.sessions.includes(sessionId)) {
      sessionData.sessions.push(sessionId);
      sessionData.lastUpdated = new Date().toISOString();
      
      await fs.writeFile(sessionsFile, JSON.stringify(sessionData, null, 2), 'utf-8');
    }
  }

  /**
   * Remove session from user's session list
   */
  private async removeSessionFromUser(phoneNumber: string, sessionId: string): Promise<void> {
    const userDir = join(this.usersDir, phoneNumber);
    const sessionsFile = join(userDir, 'sessions.json');
    
    if (!existsSync(sessionsFile)) {
      return;
    }
    
    const data = await fs.readFile(sessionsFile, 'utf-8');
    const sessionData: SessionPersistenceData = JSON.parse(data);
    
    const index = sessionData.sessions.indexOf(sessionId);
    if (index > -1) {
      sessionData.sessions.splice(index, 1);
      sessionData.lastUpdated = new Date().toISOString();
      
      await fs.writeFile(sessionsFile, JSON.stringify(sessionData, null, 2), 'utf-8');
    }
  }

  /**
   * Archive a session
   */
  private async archiveSession(session: WebsiteSession): Promise<void> {
    await this.persistSession(session);
  }

  /**
   * Shutdown the session manager
   */
  async shutdown(): Promise<void> {
    try {
      // Clear cleanup interval
      if (this.cleanupInterval) {
        clearTimeout(this.cleanupInterval);
      }
      
      // Persist any cached sessions
      for (const session of Array.from(this.sessionCache.values())) {
        await this.persistSession(session);
      }
      
      // Clear cache
      this.sessionCache.clear();
      
      console.log('[SessionManager] Shutdown complete');
    } catch (error) {
      console.error('[SessionManager] Error during shutdown:', error);
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<{
    total: number;
    active: number;
    completed: number;
    failed: number;
    timeout: number;
  }> {
    try {
      const activeSessions = await this.getActiveSessions();
      const activeFiles = await fs.readdir(this.activeDir);
      const archiveFiles = await fs.readdir(this.archiveDir);
      
      let completed = 0;
      let failed = 0;
      let timeout = 0;
      
      // Count archive sessions by state
      for (const file of archiveFiles) {
        if (file.endsWith('.json')) {
          const sessionId = file.replace('.json', '');
          const session = await this.loadSessionFromFile(sessionId);
          
          if (session) {
            switch (session.state) {
              case 'completed':
                completed++;
                break;
              case 'failed':
                failed++;
                break;
              case 'timeout':
                timeout++;
                break;
            }
          }
        }
      }
      
      return {
        total: activeFiles.length + archiveFiles.length,
        active: activeSessions.length,
        completed,
        failed,
        timeout
      };
    } catch (error) {
      console.error('[SessionManager] Failed to get session stats:', error);
      return {
        total: 0,
        active: 0,
        completed: 0,
        failed: 0,
        timeout: 0
      };
    }
  }
}