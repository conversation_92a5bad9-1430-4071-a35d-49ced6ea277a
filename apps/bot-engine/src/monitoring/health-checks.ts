/**
 * Enhanced Health Check System for Production Deployment
 * Provides comprehensive health monitoring for Fly.io deployment
 */

import { performance } from 'perf_hooks';
import { promisify } from 'util';
import { exec } from 'child_process';
import { readFileSync } from 'fs';

import { Request, Response } from 'express';

import { BotEngine } from '../core/bot-engine.js';
import { SessionManager } from '../session/session-manager.js';
import { WebsiteManager } from '../website/website-manager.js';

const execAsync = promisify(exec);

export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  checks: {
    [key: string]: {
      status: 'pass' | 'fail' | 'warn';
      latency?: number;
      error?: string;
      details?: Record<string, unknown>;
    };
  };
}

export interface ReadinessCheckResult {
  status: 'ready' | 'not_ready';
  timestamp: string;
  services: {
    [key: string]: {
      status: 'ready' | 'not_ready';
      details?: Record<string, unknown>;
    };
  };
}

export interface StartupCheckResult {
  status: 'started' | 'starting' | 'failed';
  timestamp: string;
  startup_time: number;
  initialization_steps: {
    [key: string]: {
      status: 'completed' | 'in_progress' | 'failed';
      duration?: number;
      error?: string;
    };
  };
}

export class HealthCheckService {
  private botEngine: BotEngine;
  private sessionManager: SessionManager;
  private websiteManager: WebsiteManager;
  private startupTime: number;
  private initializationSteps: Map<
    string,
    { status: 'in_progress' | 'completed' | 'failed'; duration?: number; error?: string }
  > = new Map();

  constructor(
    botEngine: BotEngine,
    sessionManager: SessionManager,
    websiteManager: WebsiteManager,
  ) {
    this.botEngine = botEngine;
    this.sessionManager = sessionManager;
    this.websiteManager = websiteManager;
    this.startupTime = Date.now();
  }

  /**
   * Comprehensive health check endpoint
   */
  async healthCheck(_req: Request, res: Response): Promise<void> {
    const startTime = performance.now();

    try {
      const result: HealthCheckResult = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: this.getVersion(),
        checks: {},
      };

      // System health checks
      const systemCheck = await this.checkSystem();
      result.checks.system = systemCheck;

      // Database/Storage health checks
      const storageCheck = await this.checkStorage();
      result.checks.storage = storageCheck;

      // External services health checks
      const servicesCheck = await this.checkExternalServices();
      result.checks.external_services = servicesCheck;

      // Application components health checks
      const componentsCheck = await this.checkApplicationComponents();
      result.checks.application_components = componentsCheck;

      // Memory and resource checks
      const resourceCheck = await this.checkResources();
      result.checks.resources = resourceCheck;

      // Determine overall health status
      const allChecks = Object.values(result.checks);
      const failedChecks = allChecks.filter(check => check.status === 'fail');
      const warnChecks = allChecks.filter(check => check.status === 'warn');

      if (failedChecks.length > 0) {
        result.status = 'unhealthy';
        res.status(503);
      } else if (warnChecks.length > 0) {
        result.status = 'degraded';
        res.status(200);
      } else {
        result.status = 'healthy';
        res.status(200);
      }

      const endTime = performance.now();
      result.checks.health_check_latency = {
        status: 'pass',
        latency: endTime - startTime,
        details: { duration_ms: endTime - startTime },
      };

      res.json(result);
    } catch (error) {
      console.error('[HealthCheck] Health check failed:', error);

      const errorResult: HealthCheckResult = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: this.getVersion(),
        checks: {
          health_check_error: {
            status: 'fail',
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        },
      };

      res.status(503).json(errorResult);
    }
  }

  /**
   * Readiness check endpoint
   */
  async readinessCheck(_req: Request, res: Response): Promise<void> {
    try {
      const result: ReadinessCheckResult = {
        status: 'ready',
        timestamp: new Date().toISOString(),
        services: {},
      };

      // Check bot engine readiness
      const botEngineReady = await this.checkBotEngineReadiness();
      result.services.bot_engine = botEngineReady;

      // Check session manager readiness
      const sessionManagerReady = await this.checkSessionManagerReadiness();
      result.services.session_manager = sessionManagerReady;

      // Check website manager readiness
      const websiteManagerReady = await this.checkWebsiteManagerReadiness();
      result.services.website_manager = websiteManagerReady;

      // Check external dependencies readiness
      const externalReady = await this.checkExternalReadiness();
      result.services.external_dependencies = externalReady;

      // Determine overall readiness
      const allServices = Object.values(result.services);
      const notReadyServices = allServices.filter(service => service.status === 'not_ready');

      if (notReadyServices.length > 0) {
        result.status = 'not_ready';
        res.status(503);
      } else {
        result.status = 'ready';
        res.status(200);
      }

      res.json(result);
    } catch (error) {
      console.error('[HealthCheck] Readiness check failed:', error);

      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Startup check endpoint
   */
  async startupCheck(_req: Request, res: Response): Promise<void> {
    try {
      const result: StartupCheckResult = {
        status: 'started',
        timestamp: new Date().toISOString(),
        startup_time: Date.now() - this.startupTime,
        initialization_steps: {},
      };

      // Convert initialization steps to result format
      for (const [step, details] of this.initializationSteps) {
        result.initialization_steps[step] = details;
      }

      // Check if all steps are completed
      const steps = Object.values(result.initialization_steps);
      const failedSteps = steps.filter(step => step.status === 'failed');
      const inProgressSteps = steps.filter(step => step.status === 'in_progress');

      if (failedSteps.length > 0) {
        result.status = 'failed';
        res.status(503);
      } else if (inProgressSteps.length > 0) {
        result.status = 'starting';
        res.status(202);
      } else {
        result.status = 'started';
        res.status(200);
      }

      res.json(result);
    } catch (error) {
      console.error('[HealthCheck] Startup check failed:', error);

      res.status(503).json({
        status: 'failed',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Liveness check endpoint (simple check)
   */
  async livenessCheck(_req: Request, res: Response): Promise<void> {
    try {
      // Simple liveness check - just verify the process is running
      const result = {
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        pid: process.pid,
        memory: process.memoryUsage(),
        node_version: process.version,
      };

      res.status(200).json(result);
    } catch (error) {
      console.error('[HealthCheck] Liveness check failed:', error);
      res.status(503).json({
        status: 'dead',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Record initialization step
   */
  recordInitializationStep(
    step: string,
    status: 'completed' | 'in_progress' | 'failed',
    duration?: number,
    error?: string,
  ): void {
    this.initializationSteps.set(step, {
      status,
      duration,
      error,
    });
  }

  // Private health check methods

  private async checkSystem(): Promise<any> {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      return {
        status: 'pass',
        details: {
          memory: memoryUsage,
          cpu: cpuUsage,
          platform: process.platform,
          arch: process.arch,
          node_version: process.version,
        },
      };
    } catch (error) {
      return {
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkStorage(): Promise<any> {
    try {
      const workspaceDir = process.env.WORKSPACE_BASE_DIR || '/app/workspaces';
      const sessionDir = process.env.SESSION_STORAGE_DIR || '/app/sessions';

      // Check disk space
      const { stdout: diskUsage } = await execAsync(`df -h ${workspaceDir} ${sessionDir}`);

      return {
        status: 'pass',
        details: {
          workspace_dir: workspaceDir,
          session_dir: sessionDir,
          disk_usage: diskUsage,
        },
      };
    } catch (error) {
      return {
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkExternalServices(): Promise<any> {
    try {
      const services = {
        github: await this.checkGitHubAPI(),
        vercel: await this.checkVercelAPI(),
        twilio: await this.checkTwilioAPI(),
        google: await this.checkGoogleAPI(),
      };

      const failedServices = Object.entries(services)
        .filter(([, service]) => service.status === 'fail')
        .map(([name]) => name);

      return {
        status: failedServices.length > 0 ? 'fail' : 'pass',
        details: services,
      };
    } catch (error) {
      return {
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkApplicationComponents(): Promise<any> {
    try {
      const components = {
        bot_engine: await this.checkComponent('bot_engine'),
        session_manager: await this.checkComponent('session_manager'),
        website_manager: await this.checkComponent('website_manager'),
      };

      const failedComponents = Object.entries(components)
        .filter(([, component]) => component.status === 'fail')
        .map(([name]) => name);

      return {
        status: failedComponents.length > 0 ? 'fail' : 'pass',
        details: components,
      };
    } catch (error) {
      return {
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkResources(): Promise<any> {
    try {
      const memoryUsage = process.memoryUsage();
      const memoryLimit = parseInt(process.env.MEMORY_LIMIT || '2048') * 1024 * 1024; // MB to bytes
      const memoryPercent = (memoryUsage.heapUsed / memoryLimit) * 100;

      const cpuUsage = process.cpuUsage();
      const loadAvg = require('os').loadavg();

      let status = 'pass';
      if (memoryPercent > 90) {
        status = 'fail';
      } else if (memoryPercent > 80) {
        status = 'warn';
      }

      return {
        status,
        details: {
          memory: {
            usage: memoryUsage,
            limit: memoryLimit,
            percent: memoryPercent,
          },
          cpu: {
            usage: cpuUsage,
            load_average: loadAvg,
          },
        },
      };
    } catch (error) {
      return {
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkBotEngineReadiness(): Promise<any> {
    try {
      const status = await this.botEngine.getStatus();
      return {
        status: status.isRunning ? 'ready' : 'not_ready',
        details: status,
      };
    } catch (error) {
      return {
        status: 'not_ready',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkSessionManagerReadiness(): Promise<any> {
    try {
      const stats = await this.sessionManager.getSessionStats();
      return {
        status: 'ready',
        details: stats,
      };
    } catch (error) {
      return {
        status: 'not_ready',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkWebsiteManagerReadiness(): Promise<any> {
    try {
      // Check if website manager is initialized
      return {
        status: 'ready',
        details: { initialized: true },
      };
    } catch (error) {
      return {
        status: 'not_ready',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkExternalReadiness(): Promise<any> {
    try {
      const checks = await Promise.allSettled([
        this.checkGitHubAPI(),
        this.checkVercelAPI(),
        this.checkTwilioAPI(),
      ]);

      const results = checks.map(result =>
        result.status === 'fulfilled'
          ? result.value
          : { status: 'fail', error: 'Promise rejected' },
      );

      const failedChecks = results.filter(result => result.status === 'fail');

      return {
        status: failedChecks.length > 2 ? 'not_ready' : 'ready', // Allow some failures
        details: {
          github: results[0],
          vercel: results[1],
          twilio: results[2],
        },
      };
    } catch (error) {
      return {
        status: 'not_ready',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkGitHubAPI(): Promise<any> {
    try {
      const token = process.env.GITHUB_TOKEN;
      if (!token) {
        return { status: 'warn', error: 'GitHub token not configured' };
      }

      const response = await fetch('https://api.github.com/rate_limit', {
        headers: { Authorization: `token ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        return { status: 'pass', details: data };
      } else {
        return { status: 'fail', error: `GitHub API returned ${response.status}` };
      }
    } catch (error) {
      return { status: 'fail', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async checkVercelAPI(): Promise<any> {
    try {
      const token = process.env.VERCEL_TOKEN;
      if (!token) {
        return { status: 'warn', error: 'Vercel token not configured' };
      }

      const response = await fetch('https://api.vercel.com/v2/user', {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = (await response.json()) as any;
        return { status: 'pass', details: { user: data.user?.username } };
      } else {
        return { status: 'fail', error: `Vercel API returned ${response.status}` };
      }
    } catch (error) {
      return { status: 'fail', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async checkTwilioAPI(): Promise<any> {
    try {
      const accountSid = process.env.TWILIO_ACCOUNT_SID;
      const authToken = process.env.TWILIO_AUTH_TOKEN;

      if (!accountSid || !authToken) {
        return { status: 'warn', error: 'Twilio credentials not configured' };
      }

      // Simple check - validate credentials
      const auth = Buffer.from(`${accountSid}:${authToken}`).toString('base64');
      const response = await fetch(
        `https://api.twilio.com/2010-04-01/Accounts/${accountSid}.json`,
        {
          headers: { Authorization: `Basic ${auth}` },
        },
      );

      if (response.ok) {
        const data = (await response.json()) as any;
        return { status: 'pass', details: { status: data.status } };
      } else {
        return { status: 'fail', error: `Twilio API returned ${response.status}` };
      }
    } catch (error) {
      return { status: 'fail', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async checkGoogleAPI(): Promise<any> {
    try {
      const apiKey = process.env.GOOGLE_API_KEY;
      if (!apiKey) {
        return { status: 'warn', error: 'Google API key not configured' };
      }

      return { status: 'pass', details: { configured: true } };
    } catch (error) {
      return { status: 'fail', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async checkComponent(component: string): Promise<any> {
    try {
      switch (component) {
        case 'bot_engine':
          return { status: 'pass', details: { initialized: !!this.botEngine } };
        case 'session_manager':
          return { status: 'pass', details: { initialized: !!this.sessionManager } };
        case 'website_manager':
          return { status: 'pass', details: { initialized: !!this.websiteManager } };
        default:
          return { status: 'warn', error: `Unknown component: ${component}` };
      }
    } catch (error) {
      return { status: 'fail', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private getVersion(): string {
    try {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf-8'));
      return packageJson.version || '0.1.0';
    } catch {
      return '0.1.0';
    }
  }
}

export function createHealthCheckService(
  botEngine: BotEngine,
  sessionManager: SessionManager,
  websiteManager: WebsiteManager,
): HealthCheckService {
  return new HealthCheckService(botEngine, sessionManager, websiteManager);
}
