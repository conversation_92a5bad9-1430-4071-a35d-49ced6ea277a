/**
 * Message Queue - Reliable message delivery system
 * Handles queuing, retrying, and rate limiting of WhatsApp messages
 */

import { EventEmitter } from 'events';

import type { User } from '@whatsite-bot/core';

import type { StatusMessageType } from '../communication/status-messenger.js';

export interface MessageTask {
  id: string;
  user: User;
  type: StatusMessageType;
  content: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  attempts: number;
  maxAttempts: number;
  delay: number;
  createdAt: Date;
  scheduledAt?: Date;
  lastAttemptAt?: Date;
  data?: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
}

export interface MessageQueueConfig {
  maxConcurrent: number;
  retryAttempts: number;
  retryDelay: number;
  processingTimeout?: number;
  maxQueueSize?: number;
}

export interface MessageQueueEvents {
  'message:queued': (task: MessageTask) => void;
  'message:processing': (task: MessageTask) => void;
  'message:completed': (task: MessageTask) => void;
  'message:failed': (task: MessageTask, error: Error) => void;
  'message:retry': (task: MessageTask, attempt: number) => void;
  'message:cancelled': (task: MessageTask) => void;
  'queue:full': (queueSize: number) => void;
  'queue:empty': () => void;
  error: (error: Error) => void;
}



/**
 * MessageQueue class for reliable message delivery
 */
export class MessageQueue extends EventEmitter {
  private config: MessageQueueConfig;
  private queue: MessageTask[] = [];
  private processing: Map<string, MessageTask> = new Map();
  private processingCount: number = 0;
  private isRunning: boolean = false;
  private processIntervalId?: NodeJS.Timeout;

  constructor(config: MessageQueueConfig) {
    super();
    this.config = {
      processingTimeout: 30000,
      maxQueueSize: 1000,
      ...config,
    };
  }

  /**
   * Initialize the message queue
   */
  async initialize(): Promise<void> {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.startProcessing();

    console.log('[MessageQueue] Initialized with config:', {
      maxConcurrent: this.config.maxConcurrent,
      retryAttempts: this.config.retryAttempts,
      retryDelay: this.config.retryDelay,
    });
  }

  /**
   * Add message to queue
   */
  async addMessage(task: MessageTask): Promise<void> {
    if (!this.isRunning) {
      throw new Error('MessageQueue not initialized');
    }

    // Check queue size limit
    if (this.config.maxQueueSize && this.queue.length >= this.config.maxQueueSize) {
      console.warn(`[MessageQueue] Queue full, rejecting message ${task.id}`);
      this.emit('queue:full', this.queue.length);
      throw new Error('Message queue is full');
    }

    // Set initial status
    task.status = 'pending';
    task.scheduledAt = new Date();

    // Insert based on priority
    this.insertByPriority(task);

    console.log(`[MessageQueue] Added message ${task.id} to queue (priority: ${task.priority})`);
    this.emit('message:queued', task);
  }

  /**
   * Get queue status
   */
  getStatus(): Record<string, any> {
    return {
      queueSize: this.queue.length,
      processingCount: this.processingCount,
      isRunning: this.isRunning,
      totalProcessed: this.getTotalProcessed(),
      priorityBreakdown: this.getPriorityBreakdown(),
    };
  }

  /**
   * Cancel message by ID
   */
  cancelMessage(messageId: string): boolean {
    // Check if message is in queue
    const queueIndex = this.queue.findIndex(task => task.id === messageId);
    if (queueIndex !== -1) {
      const task = this.queue.splice(queueIndex, 1)[0];
      if (task) {
        task.status = 'cancelled';

        console.log(`[MessageQueue] Cancelled queued message ${messageId}`);
        this.emit('message:cancelled', task);
        return true;
      }
    }

    // Check if message is being processed
    const processingTask = this.processing.get(messageId);
    if (processingTask) {
      processingTask.status = 'cancelled';

      console.log(`[MessageQueue] Cancelled processing message ${messageId}`);
      this.emit('message:cancelled', processingTask);
      return true;
    }

    return false;
  }

  /**
   * Clear all messages for a user
   */
  clearUserMessages(userPhone: string): number {
    let clearedCount = 0;

    // Clear from queue
    this.queue = this.queue.filter(task => {
      if (task.user.phoneNumber === userPhone) {
        task.status = 'cancelled';
        this.emit('message:cancelled', task);
        clearedCount++;
        return false;
      }
      return true;
    });

    // Cancel processing messages
    for (const [, task] of this.processing) {
      if (task.user.phoneNumber === userPhone) {
        task.status = 'cancelled';
        this.emit('message:cancelled', task);
        clearedCount++;
      }
    }

    console.log(`[MessageQueue] Cleared ${clearedCount} messages for user ${userPhone}`);
    return clearedCount;
  }

  /**
   * Pause message processing
   */
  pause(): void {
    if (this.processIntervalId) {
      clearInterval(this.processIntervalId);
      this.processIntervalId = undefined;
    }
    console.log('[MessageQueue] Paused processing');
  }

  /**
   * Resume message processing
   */
  resume(): void {
    if (!this.processIntervalId && this.isRunning) {
      this.startProcessing();
      console.log('[MessageQueue] Resumed processing');
    }
  }

  /**
   * Shutdown the message queue
   */
  async shutdown(): Promise<void> {
    console.log('[MessageQueue] Shutting down...');

    this.isRunning = false;

    if (this.processIntervalId) {
      clearInterval(this.processIntervalId);
      this.processIntervalId = undefined;
    }

    // Wait for processing tasks to complete
    const maxWaitTime = 10000; // 10 seconds
    const waitStart = Date.now();

    while (this.processingCount > 0 && Date.now() - waitStart < maxWaitTime) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Cancel remaining tasks
    for (const task of this.queue) {
      task.status = 'cancelled';
      this.emit('message:cancelled', task);
    }
    this.queue = [];

    for (const [, task] of this.processing) {
      task.status = 'cancelled';
      this.emit('message:cancelled', task);
    }
    this.processing.clear();

    console.log('[MessageQueue] Shutdown complete');
  }

  // Private methods

  /**
   * Start processing messages
   */
  private startProcessing(): void {
    this.processIntervalId = setInterval(() => {
      this.processQueue();
    }, 100); // Check every 100ms
  }

  /**
   * Process queued messages
   */
  private processQueue(): void {
    if (!this.isRunning) {
      return;
    }

    // Process messages while we have capacity
    while (this.processingCount < this.config.maxConcurrent && this.queue.length > 0) {
      const task = this.queue.shift();
      if (!task) break;

      // Check if task is cancelled
      if (task.status === 'cancelled') {
        continue;
      }

      // Check if task should be delayed
      if (task.scheduledAt && task.scheduledAt.getTime() > Date.now()) {
        // Put back in queue for later
        this.queue.unshift(task);
        break;
      }

      this.processMessage(task);
    }

    // Emit queue empty event
    if (this.queue.length === 0 && this.processingCount === 0) {
      this.emit('queue:empty');
    }
  }

  /**
   * Process a single message
   */
  private async processMessage(task: MessageTask): Promise<void> {
    this.processingCount++;
    this.processing.set(task.id, task);
    task.status = 'processing';
    task.lastAttemptAt = new Date();

    console.log(
      `[MessageQueue] Processing message ${task.id} (attempt ${task.attempts + 1}/${task.maxAttempts})`,
    );
    this.emit('message:processing', task);

    try {
      // Simulate message processing (in real implementation, this would call Twilio)
      await this.sendMessage(task);

      // Mark as completed
      task.status = 'completed';
      this.processing.delete(task.id);
      this.processingCount--;

      console.log(`[MessageQueue] Message ${task.id} completed successfully`);
      this.emit('message:completed', task);
    } catch (error) {
      task.attempts++;
      this.processing.delete(task.id);
      this.processingCount--;

      const err = error as Error;
      console.error(`[MessageQueue] Message ${task.id} failed:`, err.message);

      if (task.attempts < task.maxAttempts && task.status === 'processing') {
        // Schedule retry
        const retryDelay = this.calculateRetryDelay(task.attempts);
        task.scheduledAt = new Date(Date.now() + retryDelay);
        task.status = 'pending';

        // Add back to queue
        this.insertByPriority(task);

        console.log(`[MessageQueue] Scheduling retry for message ${task.id} in ${retryDelay}ms`);
        this.emit('message:retry', task, task.attempts);
      } else {
        // Max attempts reached or cancelled
        task.status = 'failed';
        console.error(
          `[MessageQueue] Message ${task.id} failed permanently after ${task.attempts} attempts`,
        );
        this.emit('message:failed', task, err);
      }
    }
  }

  /**
   * Send message (placeholder for actual Twilio implementation)
   */
  private async sendMessage(_task: MessageTask): Promise<void> {
    // This is a placeholder - in the real implementation, this would call Twilio
    return new Promise((resolve, reject) => {
      // Simulate network delay
      setTimeout(() => {
        // Simulate random failures for testing
        if (Math.random() < 0.1) {
          reject(new Error('Simulated network error'));
        } else {
          resolve();
        }
      }, 100);
    });
  }

  /**
   * Insert task in queue based on priority
   */
  private insertByPriority(task: MessageTask): void {
    const priorities = { urgent: 0, high: 1, medium: 2, low: 3 };
    const taskPriority = priorities[task.priority];

    let insertIndex = this.queue.length;
    for (let i = 0; i < this.queue.length; i++) {
      const queueTask = this.queue[i];
      const queueTaskPriorityKey = queueTask?.priority || 'medium';
      const queueTaskPriority = priorities[queueTaskPriorityKey];
      if (taskPriority < queueTaskPriority) {
        insertIndex = i;
        break;
      }
    }

    this.queue.splice(insertIndex, 0, task);
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number): number {
    const baseDelay = this.config.retryDelay;
    const backoffFactor = 2;
    const maxDelay = 60000; // 1 minute max

    const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;

    return Math.round(delay + jitter);
  }

  /**
   * Get total processed messages count
   */
  private getTotalProcessed(): number {
    // This would typically be persisted in a real implementation
    return 0;
  }

  /**
   * Get priority breakdown of current queue
   */
  private getPriorityBreakdown(): Record<string, number> {
    const breakdown = { urgent: 0, high: 0, medium: 0, low: 0 };

    for (const task of this.queue) {
      breakdown[task.priority]++;
    }

    return breakdown;
  }
}

/**
 * Create MessageQueue instance
 */
export function createMessageQueue(config: MessageQueueConfig): MessageQueue {
  return new MessageQueue(config);
}

/**
 * Default MessageQueue configuration
 */
export const DEFAULT_MESSAGE_QUEUE_CONFIG: MessageQueueConfig = {
  maxConcurrent: 5,
  retryAttempts: 3,
  retryDelay: 1000,
  processingTimeout: 30000,
  maxQueueSize: 1000,
};
