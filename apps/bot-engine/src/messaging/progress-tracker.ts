/**
 * Progress Tracker - Tracks progress of long-running operations
 * Provides progress updates and time estimates for bot operations
 */

import { EventEmitter } from 'events';

export interface ProgressUpdate {
  operationType: string;
  progress: number;
  message?: string;
  startTime: Date;
  lastUpdate: Date;
  estimatedCompletion?: Date;
  stage?: string;
  metadata?: Record<string, any>;
}

export interface ProgressTrackerEvents {
  'progress:updated': (userPhone: string, operationType: string, progress: ProgressUpdate) => void;
  'progress:completed': (
    userPhone: string,
    operationType: string,
    progress: ProgressUpdate,
  ) => void;
  'progress:failed': (userPhone: string, operationType: string, error: Error) => void;
  'progress:timeout': (userPhone: string, operationType: string) => void;
}

export interface ProgressStage {
  name: string;
  weight: number;
  estimatedDuration: number;
  dependencies?: string[];
}

export interface OperationDefinition {
  name: string;
  stages: ProgressStage[];
  totalEstimatedTime: number;
  timeoutThreshold: number;
}

export declare interface ProgressTracker {
  on<K extends keyof ProgressTrackerEvents>(event: K, listener: ProgressTrackerEvents[K]): this;
  emit<K extends keyof ProgressTrackerEvents>(
    event: K,
    ...args: Parameters<ProgressTrackerEvents[K]>
  ): boolean;
}

/**
 * ProgressTracker class for tracking operation progress
 */
export class ProgressTracker extends EventEmitter {
  private progressMap: Map<string, Map<string, ProgressUpdate>> = new Map();
  private operationDefinitions: Map<string, OperationDefinition> = new Map();
  private timeoutHandlers: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    super();
    this.initializeOperationDefinitions();
  }

  /**
   * Start tracking progress for an operation
   */
  startProgress(userPhone: string, operationType: string, message?: string): void {
    const userProgress = this.getUserProgress(userPhone);
    const operationDef = this.operationDefinitions.get(operationType);

    const progress: ProgressUpdate = {
      operationType,
      progress: 0,
      message: message || `Starting ${operationType}...`,
      startTime: new Date(),
      lastUpdate: new Date(),
      estimatedCompletion: operationDef
        ? new Date(Date.now() + operationDef.totalEstimatedTime * 1000)
        : undefined,
      stage: operationDef?.stages[0]?.name || 'starting',
      metadata: {},
    };

    userProgress.set(operationType, progress);

    // Set up timeout handler
    if (operationDef && operationDef.timeoutThreshold > 0) {
      const timeoutKey = `${userPhone}:${operationType}`;
      const timeoutHandler = setTimeout(() => {
        this.emit('progress:timeout', userPhone, operationType);
      }, operationDef.timeoutThreshold * 1000);

      this.timeoutHandlers.set(timeoutKey, timeoutHandler);
    }

    console.log(`[ProgressTracker] Started tracking ${operationType} for ${userPhone}`);
    this.emit('progress:updated', userPhone, operationType, progress);
  }

  /**
   * Update progress for an operation
   */
  updateProgress(
    userPhone: string,
    operationType: string,
    progress: number,
    message?: string,
    stage?: string,
  ): void {
    const userProgress = this.getUserProgress(userPhone);
    const currentProgress = userProgress.get(operationType);

    if (!currentProgress) {
      console.warn(
        `[ProgressTracker] No progress found for ${operationType} for user ${userPhone}`,
      );
      this.startProgress(userPhone, operationType, message);
      return;
    }

    // Ensure progress is between 0 and 100
    const normalizedProgress = Math.max(0, Math.min(100, progress));

    // Calculate estimated completion time
    const now = new Date();
    const elapsedTime = now.getTime() - currentProgress.startTime.getTime();
    const estimatedTotal =
      normalizedProgress > 0 ? (elapsedTime / normalizedProgress) * 100 : undefined;
    const estimatedCompletion = estimatedTotal
      ? new Date(currentProgress.startTime.getTime() + estimatedTotal)
      : undefined;

    const updatedProgress: ProgressUpdate = {
      ...currentProgress,
      progress: normalizedProgress,
      message: message || currentProgress.message,
      lastUpdate: now,
      estimatedCompletion,
      stage: stage || currentProgress.stage,
    };

    userProgress.set(operationType, updatedProgress);

    console.log(
      `[ProgressTracker] Updated ${operationType} for ${userPhone}: ${normalizedProgress}%`,
    );
    this.emit('progress:updated', userPhone, operationType, updatedProgress);

    // Check if operation is complete
    if (normalizedProgress >= 100) {
      this.completeProgress(userPhone, operationType);
    }
  }

  /**
   * Complete progress for an operation
   */
  completeProgress(userPhone: string, operationType: string, message?: string): void {
    const userProgress = this.getUserProgress(userPhone);
    const currentProgress = userProgress.get(operationType);

    if (!currentProgress) {
      console.warn(
        `[ProgressTracker] No progress found for ${operationType} for user ${userPhone}`,
      );
      return;
    }

    const completedProgress: ProgressUpdate = {
      ...currentProgress,
      progress: 100,
      message: message || 'Operation completed successfully',
      lastUpdate: new Date(),
      stage: 'completed',
    };

    userProgress.set(operationType, completedProgress);

    // Clear timeout handler
    const timeoutKey = `${userPhone}:${operationType}`;
    const timeoutHandler = this.timeoutHandlers.get(timeoutKey);
    if (timeoutHandler) {
      clearTimeout(timeoutHandler);
      this.timeoutHandlers.delete(timeoutKey);
    }

    console.log(`[ProgressTracker] Completed ${operationType} for ${userPhone}`);
    this.emit('progress:completed', userPhone, operationType, completedProgress);
  }

  /**
   * Fail progress for an operation
   */
  failProgress(userPhone: string, operationType: string, error: Error, message?: string): void {
    const userProgress = this.getUserProgress(userPhone);
    const currentProgress = userProgress.get(operationType);

    if (!currentProgress) {
      console.warn(
        `[ProgressTracker] No progress found for ${operationType} for user ${userPhone}`,
      );
      return;
    }

    const failedProgress: ProgressUpdate = {
      ...currentProgress,
      message: message || `Operation failed: ${error.message}`,
      lastUpdate: new Date(),
      stage: 'failed',
      metadata: {
        ...currentProgress.metadata,
        error: error.message,
      },
    };

    userProgress.set(operationType, failedProgress);

    // Clear timeout handler
    const timeoutKey = `${userPhone}:${operationType}`;
    const timeoutHandler = this.timeoutHandlers.get(timeoutKey);
    if (timeoutHandler) {
      clearTimeout(timeoutHandler);
      this.timeoutHandlers.delete(timeoutKey);
    }

    console.log(`[ProgressTracker] Failed ${operationType} for ${userPhone}:`, error.message);
    this.emit('progress:failed', userPhone, operationType, error);
  }

  /**
   * Get progress for a specific operation
   */
  getProgress(userPhone: string, operationType: string): number {
    const userProgress = this.getUserProgress(userPhone);
    const progress = userProgress.get(operationType);
    return progress ? progress.progress : 0;
  }

  /**
   * Get full progress details for a specific operation
   */
  getProgressDetails(userPhone: string, operationType: string): ProgressUpdate | null {
    const userProgress = this.getUserProgress(userPhone);
    return userProgress.get(operationType) || null;
  }

  /**
   * Get all progress for a user
   */
  getUserProgress(userPhone: string): Map<string, ProgressUpdate> {
    if (!this.progressMap.has(userPhone)) {
      this.progressMap.set(userPhone, new Map());
    }
    return this.progressMap.get(userPhone)!;
  }

  /**
   * Get all active operations for a user
   */
  getActiveOperations(userPhone: string): string[] {
    const userProgress = this.getUserProgress(userPhone);
    const activeOperations: string[] = [];

    for (const [operationType, progress] of userProgress) {
      if (progress.progress < 100 && progress.stage !== 'failed') {
        activeOperations.push(operationType);
      }
    }

    return activeOperations;
  }

  /**
   * Clean up completed operations
   */
  cleanup(userPhone: string, maxAge: number = 24 * 60 * 60 * 1000): void {
    const userProgress = this.getUserProgress(userPhone);
    const now = Date.now();

    for (const [operationType, progress] of userProgress) {
      const age = now - progress.lastUpdate.getTime();
      if (age > maxAge && (progress.progress >= 100 || progress.stage === 'failed')) {
        userProgress.delete(operationType);
        console.log(`[ProgressTracker] Cleaned up ${operationType} for ${userPhone}`);
      }
    }
  }

  /**
   * Get estimated time remaining for an operation
   */
  getEstimatedTimeRemaining(userPhone: string, operationType: string): number | null {
    const progress = this.getProgressDetails(userPhone, operationType);
    if (!progress || progress.progress >= 100) {
      return null;
    }

    const now = Date.now();
    const elapsedTime = now - progress.startTime.getTime();

    if (progress.progress <= 0) {
      return null;
    }

    const estimatedTotal = (elapsedTime / progress.progress) * 100;
    const remaining = estimatedTotal - elapsedTime;

    return Math.max(0, Math.round(remaining / 1000));
  }

  /**
   * Get operation statistics
   */
  getStatistics(): Record<string, any> {
    const stats = {
      totalUsers: this.progressMap.size,
      activeOperations: 0,
      completedOperations: 0,
      failedOperations: 0,
      operationTypes: {} as Record<string, number>,
    };

    for (const [, userProgress] of this.progressMap) {
      for (const [operationType, progress] of userProgress) {
        stats.operationTypes[operationType] = (stats.operationTypes[operationType] || 0) + 1;

        if (progress.progress >= 100) {
          stats.completedOperations++;
        } else if (progress.stage === 'failed') {
          stats.failedOperations++;
        } else {
          stats.activeOperations++;
        }
      }
    }

    return stats;
  }

  /**
   * Shutdown progress tracker
   */
  shutdown(): void {
    // Clear all timeout handlers
    for (const [, handler] of this.timeoutHandlers) {
      clearTimeout(handler);
    }
    this.timeoutHandlers.clear();

    // Clear all progress data
    this.progressMap.clear();

    console.log('[ProgressTracker] Shutdown complete');
  }

  // Private methods

  /**
   * Initialize operation definitions with stages and timing
   */
  private initializeOperationDefinitions(): void {
    // Website creation operation
    this.operationDefinitions.set('create_website', {
      name: 'Website Creation',
      stages: [
        { name: 'setup', weight: 10, estimatedDuration: 5 },
        { name: 'ai_generation', weight: 40, estimatedDuration: 30 },
        { name: 'github_creation', weight: 20, estimatedDuration: 10 },
        { name: 'deployment', weight: 30, estimatedDuration: 15 },
      ],
      totalEstimatedTime: 60,
      timeoutThreshold: 300,
    });

    // Website editing operation
    this.operationDefinitions.set('edit_website', {
      name: 'Website Editing',
      stages: [
        { name: 'analysis', weight: 20, estimatedDuration: 10 },
        { name: 'ai_generation', weight: 50, estimatedDuration: 25 },
        { name: 'github_update', weight: 15, estimatedDuration: 8 },
        { name: 'deployment', weight: 15, estimatedDuration: 7 },
      ],
      totalEstimatedTime: 50,
      timeoutThreshold: 240,
    });

    // Repository import operation
    this.operationDefinitions.set('import_repository', {
      name: 'Repository Import',
      stages: [
        { name: 'validation', weight: 15, estimatedDuration: 5 },
        { name: 'cloning', weight: 30, estimatedDuration: 15 },
        { name: 'analysis', weight: 25, estimatedDuration: 10 },
        { name: 'deployment', weight: 30, estimatedDuration: 15 },
      ],
      totalEstimatedTime: 45,
      timeoutThreshold: 180,
    });

    // Scaffold generation operation
    this.operationDefinitions.set('generate_scaffold', {
      name: 'Scaffold Generation',
      stages: [
        { name: 'planning', weight: 20, estimatedDuration: 10 },
        { name: 'generation', weight: 40, estimatedDuration: 20 },
        { name: 'github_creation', weight: 20, estimatedDuration: 10 },
        { name: 'deployment', weight: 20, estimatedDuration: 10 },
      ],
      totalEstimatedTime: 50,
      timeoutThreshold: 200,
    });

    console.log('[ProgressTracker] Initialized operation definitions');
  }
}

/**
 * Create ProgressTracker instance
 */
export function createProgressTracker(): ProgressTracker {
  return new ProgressTracker();
}
