/**
 * Optimization Utilities - Performance utility functions and helpers
 * Provides various optimization techniques and utilities
 */

import { performance } from 'perf_hooks';
import { cpuUsage, memoryUsage } from 'process';

/**
 * Memoization configuration
 */
export interface MemoizeConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
  keyGenerator?: (...args: unknown[]) => string;
  onEvict?: (key: string, value: unknown) => void;
}

/**
 * Batch processing configuration
 */
export interface BatchConfig {
  maxSize: number;
  maxWait: number; // Maximum wait time in milliseconds
  maxParallel: number; // Maximum parallel processing
}

/**
 * Object pool configuration
 */
export interface ObjectPoolConfig<T> {
  maxSize: number;
  factory: () => T;
  reset?: (obj: T) => void;
  validate?: (obj: T) => boolean;
  destroy?: (obj: T) => void;
}

/**
 * Throttle configuration
 */
export interface ThrottleConfig {
  wait: number; // Wait time in milliseconds
  leading?: boolean;
  trailing?: boolean;
}

/**
 * Debounce configuration
 */
export interface DebounceConfig {
  wait: number; // Wait time in milliseconds
  immediate?: boolean;
}

/**
 * Memoization cache entry
 */
interface MemoizeEntry<T = unknown> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * Memoization function with LRU cache and TTL
 */
export function memoize<T extends (...args: unknown[]) => unknown>(
  fn: T,
  config: MemoizeConfig,
): T & { cache: Map<string, MemoizeEntry>; clear: () => void } {
  const cache = new Map<string, MemoizeEntry>();
  const { maxSize, ttl, keyGenerator, onEvict } = config;

  const generateKey = keyGenerator || ((...args: unknown[]) => JSON.stringify(args));

  const memoized = ((...args: unknown[]) => {
    const key = generateKey(...args);
    const now = Date.now();

    // Check if cached result exists and is still valid
    const cached = cache.get(key);
    if (cached && now - cached.timestamp < ttl) {
      cached.accessCount++;
      cached.lastAccessed = now;
      return cached.value;
    }

    // Execute function and cache result
    const result = fn(...args);

    // Check cache size and evict if necessary
    if (cache.size >= maxSize) {
      // Evict least recently used item
      let lruKey = '';
      let lruTime = Number.MAX_SAFE_INTEGER;

      for (const [k, entry] of cache) {
        if (entry.lastAccessed < lruTime) {
          lruTime = entry.lastAccessed;
          lruKey = k;
        }
      }

      if (lruKey) {
        const evicted = cache.get(lruKey);
        cache.delete(lruKey);
        if (onEvict && evicted) {
          onEvict(lruKey, evicted.value);
        }
      }
    }

    cache.set(key, {
      value: result,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
    });

    return result;
  }) as T & { cache: Map<string, MemoizeEntry>; clear: () => void };

  memoized.cache = cache;
  memoized.clear = () => {
    cache.clear();
  };

  return memoized;
}

/**
 * Debounce function - delays execution until after wait milliseconds have elapsed
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  fn: T,
  config: DebounceConfig,
): T & { cancel: () => void; flush: () => unknown } {
  let timeout: NodeJS.Timeout | null = null;
  let result: unknown;
  const { wait, immediate } = config;

  const debounced = ((...args: unknown[]) => {
    const later = () => {
      timeout = null;
      if (!immediate) {
        result = fn(...args);
      }
    };

    const callNow = immediate && !timeout;

    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(later, wait);

    if (callNow) {
      result = fn(...args);
    }

    return result;
  }) as T & { cancel: () => void; flush: () => unknown };

  debounced.cancel = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };

  debounced.flush = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
      return fn();
    }
    return result;
  };

  return debounced;
}

/**
 * Throttle function - limits execution to once per wait milliseconds
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  fn: T,
  config: ThrottleConfig,
): T & { cancel: () => void } {
  let timeout: NodeJS.Timeout | null = null;
  let previous = 0;
  let result: unknown;
  const { wait, leading = true, trailing = true } = config;

  const throttled = ((...args: unknown[]) => {
    const now = Date.now();

    if (!previous && !leading) {
      previous = now;
    }

    const remaining = wait - (now - previous);

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }

      previous = now;
      result = fn(...args);
    } else if (!timeout && trailing) {
      timeout = setTimeout(() => {
        previous = !leading ? 0 : Date.now();
        timeout = null;
        result = fn(...args);
      }, remaining);
    }

    return result;
  }) as T & { cancel: () => void };

  throttled.cancel = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    previous = 0;
  };

  return throttled;
}

/**
 * Object pool for reusing objects
 */
export class ObjectPool<T> {
  private pool: T[] = [];
  private config: ObjectPoolConfig<T>;
  private created: number = 0;
  private reused: number = 0;
  private destroyed: number = 0;

  constructor(config: ObjectPoolConfig<T>) {
    this.config = config;
  }

  /**
   * Acquire object from pool
   */
  acquire(): T {
    let obj: T;

    if (this.pool.length > 0) {
      obj = this.pool.pop()!;
      this.reused++;

      // Validate object if validator provided
      if (this.config.validate && !this.config.validate(obj)) {
        if (this.config.destroy) {
          this.config.destroy(obj);
        }
        this.destroyed++;
        return this.acquire(); // Try again
      }
    } else {
      obj = this.config.factory();
      this.created++;
    }

    return obj;
  }

  /**
   * Release object back to pool
   */
  release(obj: T): void {
    if (this.pool.length < this.config.maxSize) {
      // Reset object if reset function provided
      if (this.config.reset) {
        this.config.reset(obj);
      }

      this.pool.push(obj);
    } else {
      // Pool is full, destroy object
      if (this.config.destroy) {
        this.config.destroy(obj);
      }
      this.destroyed++;
    }
  }

  /**
   * Get pool statistics
   */
  getStats(): {
    poolSize: number;
    maxSize: number;
    created: number;
    reused: number;
    destroyed: number;
    utilization: number;
  } {
    return {
      poolSize: this.pool.length,
      maxSize: this.config.maxSize,
      created: this.created,
      reused: this.reused,
      destroyed: this.destroyed,
      utilization: ((this.config.maxSize - this.pool.length) / this.config.maxSize) * 100,
    };
  }

  /**
   * Clear the pool
   */
  clear(): void {
    if (this.config.destroy) {
      for (const obj of this.pool) {
        this.config.destroy(obj);
      }
    }
    this.pool = [];
  }
}

/**
 * Batch processor for efficient bulk operations
 */
export class BatchProcessor<T, R> {
  private queue: T[] = [];
  private processing: boolean = false;
  private config: BatchConfig;
  private processor: (items: T[]) => Promise<R[]>;
  private timer: NodeJS.Timeout | null = null;
  private pendingPromises: Array<{
    resolve: (value: R) => void;
    reject: (error: Error) => void;
  }> = [];

  constructor(processor: (items: T[]) => Promise<R[]>, config: BatchConfig) {
    this.processor = processor;
    this.config = config;
  }

  /**
   * Add item to batch
   */
  add(item: T): Promise<R> {
    return new Promise<R>((resolve, reject) => {
      this.queue.push(item);
      this.pendingPromises.push({ resolve, reject });

      // Process immediately if batch is full
      if (this.queue.length >= this.config.maxSize) {
        this.processBatch().catch(console.error);
      } else if (!this.timer) {
        // Set timer for maximum wait time
        this.timer = setTimeout(() => {
          this.processBatch().catch(console.error);
        }, this.config.maxWait);
      }
    });
  }

  /**
   * Process current batch
   */
  private async processBatch(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    const items = this.queue.splice(0, this.config.maxSize);
    const promises = this.pendingPromises.splice(0, items.length);

    try {
      const results = await this.processor(items);

      // Resolve promises with results
      for (let i = 0; i < promises.length; i++) {
        const promise = promises[i];
        const result = results[i];
        if (promise && result !== undefined) {
          promise.resolve(result);
        }
      }
    } catch (error) {
      // Reject all promises with error
      for (const promise of promises) {
        promise.reject(error as Error);
      }
    } finally {
      this.processing = false;

      // Process remaining items if any
      if (this.queue.length > 0) {
        setImmediate(() => {
          this.processBatch().catch(console.error);
        });
      }
    }
  }

  /**
   * Get batch statistics
   */
  getStats(): {
    queueSize: number;
    processing: boolean;
    pendingPromises: number;
  } {
    return {
      queueSize: this.queue.length,
      processing: this.processing,
      pendingPromises: this.pendingPromises.length,
    };
  }

  /**
   * Clear the batch queue
   */
  clear(): void {
    this.queue = [];

    // Reject all pending promises
    for (const promise of this.pendingPromises) {
      promise.reject(new Error('Batch processor cleared'));
    }
    this.pendingPromises = [];

    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
}

/**
 * Lazy loading utility
 */
export class LazyLoader<T> {
  private value: T | null = null;
  private loading: boolean = false;
  private loadPromise: Promise<T> | null = null;
  private loader: () => Promise<T>;
  private ttl: number;
  private loadedAt: number = 0;

  constructor(loader: () => Promise<T>, ttl: number = 300000) {
    // 5 minutes default
    this.loader = loader;
    this.ttl = ttl;
  }

  /**
   * Get value, loading if necessary
   */
  async get(): Promise<T> {
    const now = Date.now();

    // Return cached value if still valid
    if (this.value && now - this.loadedAt < this.ttl) {
      return this.value;
    }

    // Return existing load promise if already loading
    if (this.loading && this.loadPromise) {
      return this.loadPromise;
    }

    // Start loading
    this.loading = true;
    this.loadPromise = this.loader()
      .then(value => {
        this.value = value;
        this.loadedAt = Date.now();
        this.loading = false;
        return value;
      })
      .catch(error => {
        this.loading = false;
        this.loadPromise = null;
        throw error;
      });

    return this.loadPromise;
  }

  /**
   * Check if value is loaded and valid
   */
  isLoaded(): boolean {
    return this.value !== null && Date.now() - this.loadedAt < this.ttl;
  }

  /**
   * Clear cached value
   */
  clear(): void {
    this.value = null;
    this.loadedAt = 0;
  }
}

/**
 * Performance profiler
 */
export class PerformanceProfiler {
  private timings: Map<string, number> = new Map();
  private counters: Map<string, number> = new Map();
  private markers: Map<string, { start: number; end?: number }> = new Map();

  /**
   * Start timing an operation
   */
  startTimer(name: string): void {
    this.timings.set(name, performance.now());
  }

  /**
   * End timing an operation
   */
  endTimer(name: string): number {
    const start = this.timings.get(name);
    if (!start) {
      throw new Error(`Timer '${name}' was not started`);
    }

    const duration = performance.now() - start;
    this.timings.delete(name);

    return duration;
  }

  /**
   * Time a function execution
   */
  time<T>(name: string, fn: () => T): T {
    this.startTimer(name);
    try {
      const result = fn();
      return result;
    } finally {
      this.endTimer(name);
    }
  }

  /**
   * Time an async function execution
   */
  async timeAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(name);
    try {
      const result = await fn();
      return result;
    } finally {
      this.endTimer(name);
    }
  }

  /**
   * Increment a counter
   */
  increment(name: string, value: number = 1): void {
    const current = this.counters.get(name) || 0;
    this.counters.set(name, current + value);
  }

  /**
   * Set a performance marker
   */
  mark(name: string): void {
    this.markers.set(name, { start: performance.now() });
  }

  /**
   * Measure time between markers
   */
  measure(name: string, startMark: string, endMark?: string): number {
    const start = this.markers.get(startMark);
    if (!start) {
      throw new Error(`Start marker '${startMark}' not found`);
    }

    const endTime = endMark ? this.markers.get(endMark)?.start : performance.now();
    if (endTime === undefined) {
      throw new Error(`End marker '${endMark}' not found`);
    }

    const duration = endTime - start.start;
    this.markers.set(name, { start: start.start, end: endTime });

    return duration;
  }

  /**
   * Get all performance data
   */
  getReport(): {
    timings: Record<string, number>;
    counters: Record<string, number>;
    markers: Record<string, { start: number; end?: number; duration?: number }>;
    memory: NodeJS.MemoryUsage;
    cpu: NodeJS.CpuUsage;
  } {
    const timingReport: Record<string, number> = {};
    const counterReport: Record<string, number> = {};
    const markerReport: Record<string, { start: number; end?: number; duration?: number }> = {};

    // Convert timings
    for (const [name, time] of this.timings) {
      timingReport[name] = time;
    }

    // Convert counters
    for (const [name, count] of this.counters) {
      counterReport[name] = count;
    }

    // Convert markers
    for (const [name, marker] of this.markers) {
      markerReport[name] = {
        start: marker.start,
        end: marker.end,
        duration: marker.end ? marker.end - marker.start : undefined,
      };
    }

    return {
      timings: timingReport,
      counters: counterReport,
      markers: markerReport,
      memory: memoryUsage(),
      cpu: cpuUsage(),
    };
  }

  /**
   * Clear all performance data
   */
  clear(): void {
    this.timings.clear();
    this.counters.clear();
    this.markers.clear();
  }
}

/**
 * Memory optimization utilities
 */
export class MemoryOptimizer {
  private static instance: MemoryOptimizer;
  private gcThreshold: number = 100 * 1024 * 1024; // 100MB
  private lastGC: number = 0;
  private gcInterval: number = 60000; // 1 minute
  private memoryHistory: number[] = [];
  private leakThreshold: number = 10 * 1024 * 1024; // 10MB

  private constructor() {}

  static getInstance(): MemoryOptimizer {
    if (!MemoryOptimizer.instance) {
      MemoryOptimizer.instance = new MemoryOptimizer();
    }
    return MemoryOptimizer.instance;
  }

  /**
   * Monitor memory usage and trigger GC if needed
   */
  monitor(): void {
    const memStats = memoryUsage();
    const now = Date.now();

    // Record memory usage
    this.memoryHistory.push(memStats.heapUsed);

    // Keep only last 10 measurements
    if (this.memoryHistory.length > 10) {
      this.memoryHistory.shift();
    }

    // Check for memory leaks
    if (this.memoryHistory.length >= 5) {
      const latest = this.memoryHistory[this.memoryHistory.length - 1];
      const earliest = this.memoryHistory[0];
      if (latest !== undefined && earliest !== undefined) {
        const growth = latest - earliest;
        if (growth > this.leakThreshold) {
          console.warn(
            `[MemoryOptimizer] Potential memory leak detected: ${Math.round(growth / 1024 / 1024)}MB growth`,
          );
        }
      }
    }

    // Trigger GC if needed
    if (memStats.heapUsed > this.gcThreshold && now - this.lastGC > this.gcInterval) {
      this.forceGC();
    }
  }

  /**
   * Force garbage collection
   */
  forceGC(): void {
    if (global.gc) {
      const before = memoryUsage().heapUsed;
      global.gc();
      const after = memoryUsage().heapUsed;
      const freed = before - after;

      console.log(`[MemoryOptimizer] GC: ${Math.round(freed / 1024 / 1024)}MB freed`);
      this.lastGC = Date.now();
    }
  }

  /**
   * Get memory statistics
   */
  getStats(): {
    current: NodeJS.MemoryUsage;
    history: number[];
    gcThreshold: number;
    lastGC: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  } {
    const trend = this.calculateTrend();

    return {
      current: memoryUsage(),
      history: [...this.memoryHistory],
      gcThreshold: this.gcThreshold,
      lastGC: this.lastGC,
      trend,
    };
  }

  /**
   * Calculate memory usage trend
   */
  private calculateTrend(): 'increasing' | 'decreasing' | 'stable' {
    if (this.memoryHistory.length < 3) {
      return 'stable';
    }

    const recent = this.memoryHistory.slice(-3);
    const older = this.memoryHistory.slice(-6, -3);

    if (recent.length === 0 || older.length === 0) {
      return 'stable';
    }

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const olderAvg = older.reduce((sum, val) => sum + val, 0) / older.length;

    const changePercent = ((recentAvg - olderAvg) / olderAvg) * 100;

    if (changePercent > 10) return 'increasing';
    if (changePercent < -10) return 'decreasing';
    return 'stable';
  }

  /**
   * Set GC threshold
   */
  setGCThreshold(threshold: number): void {
    this.gcThreshold = threshold;
  }

  /**
   * Set GC interval
   */
  setGCInterval(interval: number): void {
    this.gcInterval = interval;
  }
}

/**
 * Async optimization utilities
 */
export class AsyncOptimizer {
  /**
   * Parallel execution with concurrency limit
   */
  static async parallel<T>(tasks: (() => Promise<T>)[], concurrency: number = 5): Promise<T[]> {
    const results: T[] = [];
    const executing: Promise<void>[] = [];

    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];

      if (task) {
        const promise = task().then(result => {
          results[i] = result;
        });

        executing.push(promise);

        if (executing.length >= concurrency) {
          await Promise.race(executing);
          executing.splice(
            executing.findIndex(p => p === promise),
            1,
          );
        }
      }
    }

    await Promise.all(executing);
    return results;
  }

  /**
   * Retry with exponential backoff
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000,
    maxDelay: number = 30000,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxAttempts) {
          throw lastError;
        }

        const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Timeout wrapper
   */
  static async timeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    timeoutError?: Error,
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(timeoutError || new Error(`Operation timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Circuit breaker pattern
   */
  static createCircuitBreaker<T extends (...args: unknown[]) => Promise<unknown>>(
    fn: T,
    options: {
      failureThreshold: number;
      resetTimeout: number;
      monitoringWindow: number;
    },
  ): T {
    let failures = 0;
    let lastFailureTime = 0;
    let state: 'closed' | 'open' | 'half-open' = 'closed';

    const circuitBreaker = (async (...args: unknown[]) => {
      const now = Date.now();

      // Reset failure count if monitoring window has passed
      if (now - lastFailureTime > options.monitoringWindow) {
        failures = 0;
      }

      // Check circuit state
      if (state === 'open') {
        if (now - lastFailureTime > options.resetTimeout) {
          state = 'half-open';
        } else {
          throw new Error('Circuit breaker is open');
        }
      }

      try {
        const result = await fn(...args);

        // Reset on success
        if (state === 'half-open') {
          state = 'closed';
          failures = 0;
        }

        return result;
      } catch (error) {
        failures++;
        lastFailureTime = now;

        // Open circuit if failure threshold reached
        if (failures >= options.failureThreshold) {
          state = 'open';
        }

        throw error;
      }
    }) as T;

    return circuitBreaker;
  }
}

/**
 * Export all optimization utilities
 */
export default {
  memoize,
  debounce,
  throttle,
  ObjectPool,
  BatchProcessor,
  LazyLoader,
  PerformanceProfiler,
  MemoryOptimizer,
  AsyncOptimizer,
};
