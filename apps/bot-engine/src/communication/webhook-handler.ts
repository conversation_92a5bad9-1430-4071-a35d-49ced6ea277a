/**
 * Webhook event processing logic
 * Handles incoming webhook events from Vercel and processes them
 */

import { EventEmitter } from 'events';
import { randomUUID } from 'crypto';
import type {
  WebhookEvent,
  WebhookResponse,
  MessageReceivedEvent,
  WebhookHandler as IWebhook<PERSON><PERSON><PERSON>,
  WebhookEventType
} from '@whatsite-bot/core';
import { BotEngine } from '../core/bot-engine.js';

/**
 * Webhook handler configuration
 */
export interface WebhookHandlerConfig {
  /** Maximum processing time for webhooks (ms) */
  maxProcessingTime: number;
  /** Maximum retry attempts for failed webhook processing */
  maxRetries: number;
  /** Retry delay in milliseconds */
  retryDelay: number;
  /** Enable async processing */
  enableAsyncProcessing: boolean;
  /** Enable webhook event persistence */
  enableEventPersistence: boolean;
}

/**
 * Webhook processing status
 */
export interface WebhookProcessingStatus {
  /** Status of the processing */
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'timeout';
  /** Processing start time */
  startTime: Date;
  /** Processing end time */
  endTime?: Date;
  /** Error information if failed */
  error?: Error;
  /** Processing result */
  result?: WebhookResponse;
  /** Retry count */
  retryCount: number;
}

/**
 * Webhook handler events
 */
export interface WebhookHandlerEvents {
  'webhook:received': (event: WebhookEvent, processingId: string) => void;
  'webhook:processing': (event: WebhookEvent, processingId: string) => void;
  'webhook:completed': (event: WebhookEvent, processingId: string, result: WebhookResponse) => void;
  'webhook:failed': (event: WebhookEvent, processingId: string, error: Error) => void;
  'webhook:timeout': (event: WebhookEvent, processingId: string) => void;
  'webhook:retry': (event: WebhookEvent, processingId: string, retryCount: number) => void;
  'status:update': (sessionId: string, status: string, data?: any) => void;
}

export declare interface WebhookHandler {
  on<K extends keyof WebhookHandlerEvents>(event: K, listener: WebhookHandlerEvents[K]): this;
  emit<K extends keyof WebhookHandlerEvents>(event: K, ...args: Parameters<WebhookHandlerEvents[K]>): boolean;
}

/**
 * Default webhook handler configuration
 */
const DEFAULT_WEBHOOK_CONFIG: WebhookHandlerConfig = {
  maxProcessingTime: 300000, // 5 minutes
  maxRetries: 3,
  retryDelay: 5000, // 5 seconds
  enableAsyncProcessing: true,
  enableEventPersistence: true
};

/**
 * Webhook handler class
 * Processes webhook events and manages communication with bot engine
 */
export class WebhookHandler extends EventEmitter implements IWebhookHandler {
  private config: WebhookHandlerConfig;
  private botEngine: BotEngine;
  private processingStatus: Map<string, WebhookProcessingStatus> = new Map();
  private processingTimeouts: Map<string, NodeJS.Timeout> = new Map();

  constructor(botEngine: BotEngine, config: WebhookHandlerConfig = DEFAULT_WEBHOOK_CONFIG) {
    super();
    this.config = { ...DEFAULT_WEBHOOK_CONFIG, ...config };
    this.botEngine = botEngine;
    
    // Set up cleanup interval for old processing status
    setInterval(() => {
      this.cleanupOldProcessingStatus();
    }, 60000); // Every minute
    
    console.log('[WebhookHandler] Initialized with configuration:', this.config);
  }

  /**
   * Handle webhook event (implements WebhookHandler interface)
   */
  async handle(event: WebhookEvent): Promise<WebhookResponse> {
    const processingId = randomUUID();
    
    console.log(`[WebhookHandler] Processing webhook event: ${event.type} (${processingId})`);
    
    // Initialize processing status
    const processingStatus: WebhookProcessingStatus = {
      status: 'pending',
      startTime: new Date(),
      retryCount: 0
    };
    
    this.processingStatus.set(processingId, processingStatus);
    this.emit('webhook:received', event, processingId);
    
    try {
      // Set processing timeout
      const timeoutId = setTimeout(() => {
        this.handleTimeout(event, processingId);
      }, this.config.maxProcessingTime);
      
      this.processingTimeouts.set(processingId, timeoutId);
      
      // Process webhook event
      processingStatus.status = 'processing';
      this.emit('webhook:processing', event, processingId);
      
      const result = await this.processWebhookEvent(event, processingId);
      
      // Clear timeout
      clearTimeout(timeoutId);
      this.processingTimeouts.delete(processingId);
      
      // Update processing status
      processingStatus.status = 'completed';
      processingStatus.endTime = new Date();
      processingStatus.result = result;
      
      this.emit('webhook:completed', event, processingId, result);
      
      console.log(`[WebhookHandler] Successfully processed webhook event: ${event.type} (${processingId})`);
      
      return result;
    } catch (error) {
      // Clear timeout
      const timeoutId = this.processingTimeouts.get(processingId);
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.processingTimeouts.delete(processingId);
      }
      
      // Update processing status
      processingStatus.status = 'failed';
      processingStatus.endTime = new Date();
      processingStatus.error = error instanceof Error ? error : new Error(String(error));
      
      this.emit('webhook:failed', event, processingId, processingStatus.error);
      
      console.error(`[WebhookHandler] Failed to process webhook event: ${event.type} (${processingId}):`, error);
      
      // Attempt retry if configured
      if (processingStatus.retryCount < this.config.maxRetries) {
        return this.retryWebhookProcessing(event, processingId);
      }
      
      return {
        status: 'error',
        message: `Failed to process webhook event: ${processingStatus.error.message}`,
        retryAfter: 300 // 5 minutes
      };
    }
  }

  /**
   * Process webhook event based on type
   */
  private async processWebhookEvent(event: WebhookEvent, processingId: string): Promise<WebhookResponse> {
    switch (event.type) {
      case 'message.received':
        return this.handleMessageReceived(event as MessageReceivedEvent, processingId);
      
      case 'session.started':
        return this.handleSessionStarted(event, processingId);
      
      case 'session.completed':
        return this.handleSessionCompleted(event, processingId);
      
      case 'session.failed':
        return this.handleSessionFailed(event, processingId);
      
      case 'system.error':
        return this.handleSystemError(event, processingId);
      
      default:
        console.warn(`[WebhookHandler] Unhandled event type: ${event.type}`);
        return {
          status: 'error',
          message: `Unsupported event type: ${event.type}`
        };
    }
  }

  /**
   * Handle message received event
   */
  private async handleMessageReceived(event: MessageReceivedEvent, processingId: string): Promise<WebhookResponse> {
    try {
      const { from, message } = event.data;
      
      console.log(`[WebhookHandler] Processing message from ${from.phoneNumber}: ${message.content}`);
      
      // Send status update
      await this.sendStatusUpdate(processingId, 'Message received, processing...', {
        from: from.phoneNumber,
        messageType: message.type,
        messageId: message.id
      });
      
      // Forward to bot engine for processing
      const response = await this.botEngine.handleWebhookEvent(event);
      
      // Send completion status
      await this.sendStatusUpdate(processingId, 'Message processed successfully', {
        from: from.phoneNumber,
        response: response.message
      });
      
      return response;
    } catch (error) {
      console.error(`[WebhookHandler] Error processing message received event:`, error);
      throw error;
    }
  }

  /**
   * Handle session started event
   */
  private async handleSessionStarted(event: WebhookEvent, processingId: string): Promise<WebhookResponse> {
    try {
      console.log(`[WebhookHandler] Processing session started event: ${event.id}`);
      
      // Send status update
      await this.sendStatusUpdate(processingId, 'Session started, initializing...', {
        eventId: event.id
      });
      
      // Forward to bot engine
      const response = await this.botEngine.handleWebhookEvent(event);
      
      return response;
    } catch (error) {
      console.error(`[WebhookHandler] Error processing session started event:`, error);
      throw error;
    }
  }

  /**
   * Handle session completed event
   */
  private async handleSessionCompleted(event: WebhookEvent, processingId: string): Promise<WebhookResponse> {
    try {
      console.log(`[WebhookHandler] Processing session completed event: ${event.id}`);
      
      // Send status update
      await this.sendStatusUpdate(processingId, 'Session completed successfully', {
        eventId: event.id
      });
      
      // Forward to bot engine
      const response = await this.botEngine.handleWebhookEvent(event);
      
      return response;
    } catch (error) {
      console.error(`[WebhookHandler] Error processing session completed event:`, error);
      throw error;
    }
  }

  /**
   * Handle session failed event
   */
  private async handleSessionFailed(event: WebhookEvent, processingId: string): Promise<WebhookResponse> {
    try {
      console.log(`[WebhookHandler] Processing session failed event: ${event.id}`);
      
      // Send status update
      await this.sendStatusUpdate(processingId, 'Session failed, handling error...', {
        eventId: event.id
      });
      
      // Forward to bot engine
      const response = await this.botEngine.handleWebhookEvent(event);
      
      return response;
    } catch (error) {
      console.error(`[WebhookHandler] Error processing session failed event:`, error);
      throw error;
    }
  }

  /**
   * Handle system error event
   */
  private async handleSystemError(event: WebhookEvent, processingId: string): Promise<WebhookResponse> {
    try {
      console.log(`[WebhookHandler] Processing system error event: ${event.id}`);
      
      // Send status update
      await this.sendStatusUpdate(processingId, 'System error detected, handling...', {
        eventId: event.id
      });
      
      // Forward to bot engine
      const response = await this.botEngine.handleWebhookEvent(event);
      
      return response;
    } catch (error) {
      console.error(`[WebhookHandler] Error processing system error event:`, error);
      throw error;
    }
  }

  /**
   * Handle processing timeout
   */
  private handleTimeout(event: WebhookEvent, processingId: string): void {
    const processingStatus = this.processingStatus.get(processingId);
    if (processingStatus) {
      processingStatus.status = 'timeout';
      processingStatus.endTime = new Date();
      processingStatus.error = new Error('Webhook processing timed out');
    }
    
    this.emit('webhook:timeout', event, processingId);
    console.error(`[WebhookHandler] Webhook processing timed out: ${event.type} (${processingId})`);
  }

  /**
   * Retry webhook processing
   */
  private async retryWebhookProcessing(event: WebhookEvent, processingId: string): Promise<WebhookResponse> {
    const processingStatus = this.processingStatus.get(processingId);
    if (!processingStatus) {
      throw new Error('Processing status not found for retry');
    }
    
    processingStatus.retryCount++;
    processingStatus.status = 'pending';
    
    this.emit('webhook:retry', event, processingId, processingStatus.retryCount);
    
    console.log(`[WebhookHandler] Retrying webhook processing: ${event.type} (${processingId}) - Attempt ${processingStatus.retryCount}`);
    
    // Wait before retry
    await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
    
    return this.handle(event);
  }

  /**
   * Send status update
   */
  private async sendStatusUpdate(processingId: string, status: string, data?: any): Promise<void> {
    try {
      this.emit('status:update', processingId, status, data);
      console.log(`[WebhookHandler] Status update (${processingId}): ${status}`);
    } catch (error) {
      console.error(`[WebhookHandler] Error sending status update:`, error);
    }
  }

  /**
   * Get supported event types
   */
  getSupportedEvents(): WebhookEventType[] {
    return [
      'message.received',
      'message.sent',
      'session.started',
      'session.completed',
      'session.failed',
      'system.error'
    ];
  }

  /**
   * Validate webhook signature (optional implementation)
   */
  validateSignature(_payload: any, _signature: string): boolean {
    // This would implement signature validation logic
    // For now, return true (signature validation is handled in middleware)
    return true;
  }

  /**
   * Get processing status
   */
  getProcessingStatus(processingId: string): WebhookProcessingStatus | undefined {
    return this.processingStatus.get(processingId);
  }

  /**
   * Get all processing statuses
   */
  getAllProcessingStatuses(): Map<string, WebhookProcessingStatus> {
    return new Map(this.processingStatus);
  }

  /**
   * Clean up old processing status entries
   */
  private cleanupOldProcessingStatus(): void {
    const now = new Date();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    for (const [processingId, status] of this.processingStatus.entries()) {
      const age = now.getTime() - status.startTime.getTime();
      if (age > maxAge) {
        this.processingStatus.delete(processingId);
        console.log(`[WebhookHandler] Cleaned up old processing status: ${processingId}`);
      }
    }
  }

  /**
   * Get webhook handler statistics
   */
  getStatistics(): {
    totalProcessed: number;
    currentlyProcessing: number;
    completed: number;
    failed: number;
    timedOut: number;
    averageProcessingTime: number;
  } {
    const statuses = Array.from(this.processingStatus.values());
    
    const currentlyProcessing = statuses.filter(s => s.status === 'processing').length;
    const completed = statuses.filter(s => s.status === 'completed').length;
    const failed = statuses.filter(s => s.status === 'failed').length;
    const timedOut = statuses.filter(s => s.status === 'timeout').length;
    
    const completedStatuses = statuses.filter(s => s.status === 'completed' && s.endTime);
    const totalProcessingTime = completedStatuses.reduce((sum, s) => {
      return sum + (s.endTime!.getTime() - s.startTime.getTime());
    }, 0);
    
    const averageProcessingTime = completedStatuses.length > 0 
      ? totalProcessingTime / completedStatuses.length 
      : 0;
    
    return {
      totalProcessed: statuses.length,
      currentlyProcessing,
      completed,
      failed,
      timedOut,
      averageProcessingTime
    };
  }

  /**
   * Shutdown webhook handler
   */
  async shutdown(): Promise<void> {
    console.log('[WebhookHandler] Shutting down...');
    
    // Clear all timeouts
    for (const timeoutId of this.processingTimeouts.values()) {
      clearTimeout(timeoutId);
    }
    this.processingTimeouts.clear();
    
    // Clear processing status
    this.processingStatus.clear();
    
    console.log('[WebhookHandler] Shutdown complete');
  }
}

/**
 * Create webhook handler instance
 */
export function createWebhookHandler(botEngine: BotEngine, config?: WebhookHandlerConfig): WebhookHandler {
  return new WebhookHandler(botEngine, config);
}