/**
 * Status update communication back to Vercel/WhatsApp
 * Handles real-time status updates during webhook processing
 */

import { EventEmitter } from 'events';
import { randomUUID } from 'crypto';

import twilio from 'twilio';

/**
 * Status communication configuration
 */
export interface StatusCommunicatorConfig {
  /** Vercel webhook handler URL for status updates */
  vercelWebhookUrl?: string;
  /** API key for Vercel communication */
  vercelApiKey?: string;
  /** Twilio configuration for direct WhatsApp messages */
  twilio?: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
  };
  /** Enable status updates to WhatsApp */
  enableWhatsAppUpdates: boolean;
  /** Enable status updates to Vercel */
  enableVercelUpdates: boolean;
  /** Maximum retry attempts for status updates */
  maxRetries: number;
  /** Retry delay in milliseconds */
  retryDelay: number;
  /** Batch status updates */
  enableBatching: boolean;
  /** Batch size for status updates */
  batchSize: number;
  /** Batch timeout in milliseconds */
  batchTimeout: number;
}

/**
 * Status update message
 */
export interface StatusUpdate {
  /** Update ID */
  id: string;
  /** Session ID */
  sessionId: string;
  /** User phone number */
  phoneNumber: string;
  /** Status message */
  message: string;
  /** Status type */
  type: 'info' | 'success' | 'warning' | 'error';
  /** Status data */
  data?: any;
  /** Timestamp */
  timestamp: Date;
  /** Retry count */
  retryCount: number;
}

/**
 * Status communication events
 */
export interface StatusCommunicatorEvents {
  'status:sent': (update: StatusUpdate) => void;
  'status:failed': (update: StatusUpdate, error: Error) => void;
  'status:retry': (update: StatusUpdate, retryCount: number) => void;
  'batch:sent': (updates: StatusUpdate[]) => void;
  'batch:failed': (updates: StatusUpdate[], error: Error) => void;
}

export declare interface StatusCommunicator {
  on<K extends keyof StatusCommunicatorEvents>(
    event: K,
    listener: StatusCommunicatorEvents[K],
  ): this;
  emit<K extends keyof StatusCommunicatorEvents>(
    event: K,
    ...args: Parameters<StatusCommunicatorEvents[K]>
  ): boolean;
}

/**
 * Default status communicator configuration
 */
const DEFAULT_STATUS_CONFIG: StatusCommunicatorConfig = {
  vercelWebhookUrl: process.env.VERCEL_WEBHOOK_URL,
  vercelApiKey: process.env.VERCEL_API_KEY,
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID || '',
    authToken: process.env.TWILIO_AUTH_TOKEN || '',
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
  },
  enableWhatsAppUpdates: true,
  enableVercelUpdates: true,
  maxRetries: 3,
  retryDelay: 2000,
  enableBatching: false,
  batchSize: 5,
  batchTimeout: 5000,
};

/**
 * Status communicator class
 * Handles sending status updates back to Vercel and WhatsApp
 */
export class StatusCommunicator extends EventEmitter {
  private config: StatusCommunicatorConfig;
  private twilioClient?: twilio.Twilio;
  private pendingUpdates: Map<string, StatusUpdate> = new Map();
  private batchBuffer: StatusUpdate[] = [];
  private batchTimeout?: NodeJS.Timeout;
  private retryQueue: StatusUpdate[] = [];
  private retryTimeout?: NodeJS.Timeout;

  constructor(config: StatusCommunicatorConfig = DEFAULT_STATUS_CONFIG) {
    super();
    this.config = { ...DEFAULT_STATUS_CONFIG, ...config };

    // Initialize Twilio client if configured
    if (this.config.twilio?.accountSid && this.config.twilio?.authToken) {
      this.twilioClient = twilio(this.config.twilio.accountSid, this.config.twilio.authToken);
    }

    // Start retry processing
    this.startRetryProcessing();

    console.log('[StatusCommunicator] Initialized with configuration:', {
      enableWhatsAppUpdates: this.config.enableWhatsAppUpdates,
      enableVercelUpdates: this.config.enableVercelUpdates,
      enableBatching: this.config.enableBatching,
      twilioConfigured: !!this.twilioClient,
    });
  }

  /**
   * Send status update
   */
  async sendStatusUpdate(
    sessionId: string,
    phoneNumber: string,
    message: string,
    type: StatusUpdate['type'] = 'info',
    data?: any,
  ): Promise<void> {
    const update: StatusUpdate = {
      id: randomUUID(),
      sessionId,
      phoneNumber,
      message,
      type,
      data,
      timestamp: new Date(),
      retryCount: 0,
    };

    console.log(`[StatusCommunicator] Sending status update: ${message} (${type})`);

    this.pendingUpdates.set(update.id, update);

    try {
      if (this.config.enableBatching) {
        this.addToBatch(update);
      } else {
        await this.sendUpdate(update);
      }
    } catch (error) {
      console.error(`[StatusCommunicator] Failed to send status update:`, error);
      this.handleFailedUpdate(update, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Send success status
   */
  async sendSuccess(
    sessionId: string,
    phoneNumber: string,
    message: string,
    data?: any,
  ): Promise<void> {
    return this.sendStatusUpdate(sessionId, phoneNumber, `✅ ${message}`, 'success', data);
  }

  /**
   * Send error status
   */
  async sendError(
    sessionId: string,
    phoneNumber: string,
    message: string,
    data?: any,
  ): Promise<void> {
    return this.sendStatusUpdate(sessionId, phoneNumber, `❌ ${message}`, 'error', data);
  }

  /**
   * Send warning status
   */
  async sendWarning(
    sessionId: string,
    phoneNumber: string,
    message: string,
    data?: any,
  ): Promise<void> {
    return this.sendStatusUpdate(sessionId, phoneNumber, `⚠️ ${message}`, 'warning', data);
  }

  /**
   * Send info status
   */
  async sendInfo(
    sessionId: string,
    phoneNumber: string,
    message: string,
    data?: any,
  ): Promise<void> {
    return this.sendStatusUpdate(sessionId, phoneNumber, `📊 ${message}`, 'info', data);
  }

  /**
   * Send progress status
   */
  async sendProgress(
    sessionId: string,
    phoneNumber: string,
    message: string,
    progress?: number,
  ): Promise<void> {
    const progressText = progress !== undefined ? ` (${Math.round(progress)}%)` : '';
    return this.sendStatusUpdate(sessionId, phoneNumber, `⏳ ${message}${progressText}`, 'info', {
      progress,
    });
  }

  /**
   * Send single update
   */
  private async sendUpdate(update: StatusUpdate): Promise<void> {
    const promises: Promise<void>[] = [];

    // Send to WhatsApp via Twilio
    if (this.config.enableWhatsAppUpdates && this.twilioClient) {
      promises.push(this.sendToWhatsApp(update));
    }

    // Send to Vercel webhook
    if (this.config.enableVercelUpdates && this.config.vercelWebhookUrl) {
      promises.push(this.sendToVercel(update));
    }

    // Wait for all sends to complete
    await Promise.all(promises);

    // Mark as sent
    this.pendingUpdates.delete(update.id);
    this.emit('status:sent', update);
  }

  /**
   * Send update to WhatsApp via Twilio
   */
  private async sendToWhatsApp(update: StatusUpdate): Promise<void> {
    if (!this.twilioClient || !this.config.twilio?.phoneNumber) {
      throw new Error('Twilio not configured');
    }

    try {
      await this.twilioClient.messages.create({
        body: update.message,
        from: `whatsapp:${this.config.twilio.phoneNumber}`,
        to: `whatsapp:${update.phoneNumber}`,
      });

      console.log(`[StatusCommunicator] Status sent to WhatsApp: ${update.phoneNumber}`);
    } catch (error) {
      console.error(`[StatusCommunicator] Failed to send to WhatsApp:`, error);
      throw error;
    }
  }

  /**
   * Send update to Vercel webhook
   */
  private async sendToVercel(update: StatusUpdate): Promise<void> {
    if (!this.config.vercelWebhookUrl) {
      throw new Error('Vercel webhook URL not configured');
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'Bot-Engine-Status/1.0',
      };

      if (this.config.vercelApiKey) {
        headers['Authorization'] = `Bearer ${this.config.vercelApiKey}`;
      }

      const response = await fetch(`${this.config.vercelWebhookUrl}/status`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          update,
          timestamp: new Date().toISOString(),
          source: 'bot-engine',
        }),
        signal: AbortSignal.timeout(15000), // 15 second timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      console.log(`[StatusCommunicator] Status sent to Vercel: ${update.sessionId}`);
    } catch (error) {
      console.error(`[StatusCommunicator] Failed to send to Vercel:`, error);
      throw error;
    }
  }

  /**
   * Add update to batch
   */
  private addToBatch(update: StatusUpdate): void {
    this.batchBuffer.push(update);

    // Send batch if it's full
    if (this.batchBuffer.length >= this.config.batchSize) {
      this.sendBatch();
    }
    // Or set timeout for partial batch
    else if (!this.batchTimeout) {
      this.batchTimeout = setTimeout(() => {
        this.sendBatch();
      }, this.config.batchTimeout);
    }
  }

  /**
   * Send batch of updates
   */
  private async sendBatch(): Promise<void> {
    if (this.batchBuffer.length === 0) {
      return;
    }

    const batch = [...this.batchBuffer];
    this.batchBuffer.length = 0;

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = undefined;
    }

    try {
      // Send each update in the batch
      await Promise.all(batch.map(update => this.sendUpdate(update)));

      this.emit('batch:sent', batch);
      console.log(`[StatusCommunicator] Batch sent: ${batch.length} updates`);
    } catch (error) {
      console.error(`[StatusCommunicator] Failed to send batch:`, error);
      this.emit('batch:failed', batch, error instanceof Error ? error : new Error(String(error)));

      // Add failed updates to retry queue
      batch.forEach(update => {
        this.handleFailedUpdate(update, error instanceof Error ? error : new Error(String(error)));
      });
    }
  }

  /**
   * Handle failed update
   */
  private handleFailedUpdate(update: StatusUpdate, error: Error): void {
    this.emit('status:failed', update, error);

    // Add to retry queue if under max retries
    if (update.retryCount < this.config.maxRetries) {
      update.retryCount++;
      this.retryQueue.push(update);
      this.emit('status:retry', update, update.retryCount);

      console.log(
        `[StatusCommunicator] Queuing update for retry: ${update.id} (attempt ${update.retryCount})`,
      );
    } else {
      console.error(`[StatusCommunicator] Max retries reached for update: ${update.id}`);
      this.pendingUpdates.delete(update.id);
    }
  }

  /**
   * Start retry processing
   */
  private startRetryProcessing(): void {
    this.retryTimeout = setTimeout(() => {
      this.processRetryQueue();
    }, this.config.retryDelay);
  }

  /**
   * Process retry queue
   */
  private async processRetryQueue(): Promise<void> {
    if (this.retryQueue.length > 0) {
      const update = this.retryQueue.shift()!;

      try {
        await this.sendUpdate(update);
      } catch (error) {
        this.handleFailedUpdate(update, error instanceof Error ? error : new Error(String(error)));
      }
    }

    // Schedule next retry processing
    this.startRetryProcessing();
  }

  /**
   * Get pending updates count
   */
  getPendingCount(): number {
    return this.pendingUpdates.size;
  }

  /**
   * Get retry queue count
   */
  getRetryCount(): number {
    return this.retryQueue.length;
  }

  /**
   * Get statistics
   */
  getStatistics(): {
    pendingUpdates: number;
    retryQueue: number;
    batchBuffer: number;
    enabledChannels: string[];
  } {
    const enabledChannels: string[] = [];
    if (this.config.enableWhatsAppUpdates) enabledChannels.push('whatsapp');
    if (this.config.enableVercelUpdates) enabledChannels.push('vercel');

    return {
      pendingUpdates: this.pendingUpdates.size,
      retryQueue: this.retryQueue.length,
      batchBuffer: this.batchBuffer.length,
      enabledChannels,
    };
  }

  /**
   * Flush all pending updates
   */
  async flush(): Promise<void> {
    console.log('[StatusCommunicator] Flushing all pending updates...');

    // Send any buffered batch
    if (this.batchBuffer.length > 0) {
      await this.sendBatch();
    }

    // Process retry queue
    while (this.retryQueue.length > 0) {
      const update = this.retryQueue.shift()!;
      try {
        await this.sendUpdate(update);
      } catch (error) {
        console.error(`[StatusCommunicator] Failed to flush update: ${update.id}`, error);
      }
    }

    console.log('[StatusCommunicator] Flush complete');
  }

  /**
   * Shutdown status communicator
   */
  async shutdown(): Promise<void> {
    console.log('[StatusCommunicator] Shutting down...');

    // Clear timeouts
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }

    // Flush pending updates
    await this.flush();

    // Clear state
    this.pendingUpdates.clear();
    this.batchBuffer.length = 0;
    this.retryQueue.length = 0;

    console.log('[StatusCommunicator] Shutdown complete');
  }
}

/**
 * Create status communicator instance
 */
export function createStatusCommunicator(config?: StatusCommunicatorConfig): StatusCommunicator {
  return new StatusCommunicator(config);
}
