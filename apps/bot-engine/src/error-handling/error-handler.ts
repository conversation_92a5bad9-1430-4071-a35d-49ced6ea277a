/**
 * Central Error Handler Service
 * Provides comprehensive error handling and recovery mechanisms for the WhatsApp Website Bot
 */

import { EventEmitter } from 'events';
import { randomUUID } from 'crypto';

import { <PERSON>tError, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON> as IError<PERSON>andler } from '@whatsite-bot/core';

import { CircuitBreaker } from './circuit-breaker.js';
import { RetryManager } from './retry-manager.js';
import { RecoveryManager } from './recovery-manager.js';
import { FallbackService } from './fallback-service.js';

/**
 * Error handling configuration
 */
export interface ErrorHandlerConfig {
  /** Maximum concurrent error processing */
  maxConcurrentErrors: number;
  /** Error rate threshold for circuit breaker */
  errorRateThreshold: number;
  /** Recovery attempt timeout */
  recoveryTimeout: number;
  /** Enable automatic recovery */
  enableAutoRecovery: boolean;
  /** Enable fallback mechanisms */
  enableFallbacks: boolean;
  /** Error notification settings */
  notifications: {
    /** Notify users of errors */
    notifyUsers: boolean;
    /** Notify admins of critical errors */
    notifyAdmins: boolean;
    /** Error notification rate limit */
    rateLimitMs: number;
  };
  /** Monitoring settings */
  monitoring: {
    /** Enable error metrics collection */
    enableMetrics: boolean;
    /** Metrics aggregation interval */
    metricsIntervalMs: number;
    /** Alert thresholds */
    alertThresholds: {
      errorRate: number;
      criticalErrorRate: number;
      recoveryFailureRate: number;
    };
  };
}

/**
 * Error context information
 */
export interface ErrorContext {
  /** Error ID */
  id: string;
  /** Session ID if applicable */
  sessionId?: string;
  /** User phone number if applicable */
  phoneNumber?: string;
  /** Operation being performed */
  operation?: string;
  /** Service that caused the error */
  service?: string;
  /** Request/operation metadata */
  metadata?: Record<string, unknown>;
  /** Timestamp */
  timestamp: Date;
}

/**
 * Error processing result
 */
export interface ErrorProcessingResult {
  /** Processing ID */
  id: string;
  /** Whether error was handled */
  handled: boolean;
  /** Recovery attempted */
  recoveryAttempted: boolean;
  /** Recovery successful */
  recoverySuccessful: boolean;
  /** Fallback used */
  fallbackUsed: boolean;
  /** User notification sent */
  userNotified: boolean;
  /** Admin notification sent */
  adminNotified: boolean;
  /** Error category */
  category: string;
  /** Error severity */
  severity: string;
  /** Processing time in milliseconds */
  processingTime: number;
  /** Final error state */
  finalError?: Error;
}

/**
 * Error statistics
 */
export interface ErrorStatistics {
  /** Total errors processed */
  totalErrors: number;
  /** Errors by category */
  errorsByCategory: Record<string, number>;
  /** Errors by severity */
  errorsBySeverity: Record<string, number>;
  /** Recovery success rate */
  recoverySuccessRate: number;
  /** Fallback usage rate */
  fallbackUsageRate: number;
  /** Average processing time */
  averageProcessingTime: number;
  /** Error rate per minute */
  errorRatePerMinute: number;
  /** Circuit breaker trips */
  circuitBreakerTrips: number;
  /** Recent errors */
  recentErrors: ErrorProcessingResult[];
}

/**
 * Error handler events
 */
export interface ErrorHandlerEvents {
  'error:received': (error: Error, context: ErrorContext) => void;
  'error:processed': (result: ErrorProcessingResult) => void;
  'error:recovery:attempted': (error: Error, context: ErrorContext) => void;
  'error:recovery:success': (error: Error, context: ErrorContext) => void;
  'error:recovery:failed': (error: Error, context: ErrorContext) => void;
  'error:fallback:used': (error: Error, context: ErrorContext, fallbackType: string) => void;
  'error:notification:sent': (error: Error, context: ErrorContext, recipient: string) => void;
  'error:threshold:exceeded': (category: string, rate: number, threshold: number) => void;
  'circuit:breaker:opened': (service: string, errorRate: number) => void;
  'circuit:breaker:closed': (service: string) => void;
  'metrics:collected': (stats: ErrorStatistics) => void;
}

/**
 * Central Error Handler Service
 */
export class ErrorHandlerService extends EventEmitter implements IErrorHandler {
  private config: ErrorHandlerConfig;
  private circuitBreaker: CircuitBreaker;
  private retryManager: RetryManager;
  private recoveryManager: RecoveryManager;
  private fallbackService: FallbackService;

  private processingQueue: Array<{
    error: Error;
    context: ErrorContext;
    resolve: (result: ErrorProcessingResult) => void;
  }> = [];
  private processingResults: Map<string, ErrorProcessingResult> = new Map();
  private errorStats: ErrorStatistics;
  private lastNotificationTime: Map<string, number> = new Map();
  private processingCount = 0;
  private metricsInterval?: NodeJS.Timeout;

  constructor(config: ErrorHandlerConfig) {
    super();
    this.config = config;

    // Initialize components
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: config.errorRateThreshold,
      recoveryTimeout: config.recoveryTimeout,
      monitoringInterval: config.monitoring.metricsIntervalMs,
      minimumRequestThreshold: 5,
      failureRateWindow: 60000,
      successThreshold: 5,
    });

    this.retryManager = new RetryManager({
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      jitter: true,
      jitterFactor: 0.1,
      strategy: 'exponential',
    });

    this.recoveryManager = new RecoveryManager({
      maxRecoveryAttempts: 3,
      recoveryTimeout: config.recoveryTimeout,
      enableAutoRecovery: config.enableAutoRecovery,
      strategies: {
        SessionError: ['retry', 'fallback', 'reset'],
        WorkspaceError: ['retry', 'fallback', 'reset'],
        CommunicationError: ['retry', 'fallback'],
        AIError: ['retry', 'fallback'],
        DeploymentError: ['retry', 'fallback', 'reset'],
        SystemError: ['retry', 'reset'],
        ExternalServiceError: ['retry', 'fallback'],
        RateLimitError: ['retry', 'fallback'],
        DatabaseError: ['retry', 'reset'],
      },
      customHandlers: {},
      cooldownPeriod: 30000,
    });

    this.fallbackService = new FallbackService({
      enableFallbacks: config.enableFallbacks,
      fallbackTimeout: 5000,
      cacheExpiryMs: 300000,
      maxQueueSize: 100,
      defaultResponses: {},
      fallbackPriorities: {
        SessionError: ['simple_response', 'default_action'],
        WorkspaceError: ['simple_response', 'degraded_functionality'],
        CommunicationError: ['simple_response', 'alternative_service'],
        AIError: ['simple_response', 'alternative_service'],
        DeploymentError: ['simple_response', 'manual_intervention'],
        SystemError: ['alternative_service', 'manual_intervention'],
        ExternalServiceError: ['simple_response', 'alternative_service'],
        RateLimitError: ['simple_response', 'queue_for_later'],
        DatabaseError: ['simple_response', 'manual_intervention'],
      },
    });

    // Initialize statistics
    this.errorStats = {
      totalErrors: 0,
      errorsByCategory: {},
      errorsBySeverity: {},
      recoverySuccessRate: 0,
      fallbackUsageRate: 0,
      averageProcessingTime: 0,
      errorRatePerMinute: 0,
      circuitBreakerTrips: 0,
      recentErrors: [],
    };

    // Set up metrics collection
    if (config.monitoring.enableMetrics) {
      this.metricsInterval = setInterval(() => {
        this.collectMetrics();
      }, config.monitoring.metricsIntervalMs);
    }

    // Set up circuit breaker events
    this.circuitBreaker.on('circuit:opened', (service: string) => {
      this.emit('circuit:breaker:opened', service, this.config.errorRateThreshold);
    });

    this.circuitBreaker.on('circuit:closed', (service: string) => {
      this.emit('circuit:breaker:closed', service);
    });

    console.log('[ErrorHandler] Initialized with configuration:', {
      maxConcurrentErrors: config.maxConcurrentErrors,
      errorRateThreshold: config.errorRateThreshold,
      enableAutoRecovery: config.enableAutoRecovery,
      enableFallbacks: config.enableFallbacks,
    });
  }

  /**
   * Handle error (implements ErrorHandler interface)
   */
  async handle(error: Error, context: Record<string, unknown> = {}): Promise<void> {
    const errorContext: ErrorContext = {
      id: randomUUID(),
      sessionId: typeof context.sessionId === 'string' ? context.sessionId : undefined,
      phoneNumber: typeof context.phoneNumber === 'string' ? context.phoneNumber : undefined,
      operation: typeof context.operation === 'string' ? context.operation : undefined,
      service: typeof context.service === 'string' ? context.service : undefined,
      metadata:
        typeof context.metadata === 'object' && context.metadata !== null
          ? (context.metadata as Record<string, unknown>)
          : undefined,
      timestamp: new Date(),
    };

    const result = await this.processError(error, errorContext);
    this.emit('error:processed', result);
  }

  /**
   * Check if error should be handled
   */
  canHandle(error: Error): boolean {
    return error instanceof BotError || error instanceof Error;
  }

  /**
   * Process error with comprehensive handling
   */
  async processError(error: Error, context: ErrorContext): Promise<ErrorProcessingResult> {
    const startTime = Date.now();

    // Create processing result
    const result: ErrorProcessingResult = {
      id: context.id,
      handled: false,
      recoveryAttempted: false,
      recoverySuccessful: false,
      fallbackUsed: false,
      userNotified: false,
      adminNotified: false,
      category: error instanceof BotError ? error.category : 'unknown',
      severity: ErrorUtils.getSeverity(error),
      processingTime: 0,
      finalError: error,
    };

    try {
      console.log(`[ErrorHandler] Processing error: ${error.message} (${context.id})`);
      this.emit('error:received', error, context);

      // Check if we're at max concurrent processing
      if (this.processingCount >= this.config.maxConcurrentErrors) {
        console.warn(`[ErrorHandler] Max concurrent errors reached, queuing error: ${context.id}`);
        return this.queueError(error, context);
      }

      this.processingCount++;

      // Check circuit breaker
      if (context.service && this.circuitBreaker.isOpen(context.service)) {
        console.log(`[ErrorHandler] Circuit breaker open for service: ${context.service}`);
        result.fallbackUsed = await this.attemptFallback(error, context);
        result.handled = result.fallbackUsed;
      } else {
        // Attempt recovery if error is retryable
        if (this.config.enableAutoRecovery && ErrorUtils.isRetryable(error)) {
          result.recoveryAttempted = true;
          this.emit('error:recovery:attempted', error, context);

          result.recoverySuccessful = await this.attemptRecovery(error, context);

          if (result.recoverySuccessful) {
            this.emit('error:recovery:success', error, context);
            result.handled = true;
          } else {
            this.emit('error:recovery:failed', error, context);
          }
        }

        // If recovery failed or not attempted, try fallback
        if (!result.handled && this.config.enableFallbacks) {
          result.fallbackUsed = await this.attemptFallback(error, context);
          result.handled = result.fallbackUsed;
        }
      }

      // Record error in circuit breaker
      if (context.service) {
        if (result.handled) {
          this.circuitBreaker.recordSuccess(context.service);
        } else {
          this.circuitBreaker.recordFailure(context.service);
        }
      }

      // Send notifications
      this.sendNotifications(error, context, result);

      // Update statistics
      this.updateStatistics(result);

      // Store processing result
      this.processingResults.set(context.id, result);

      console.log(`[ErrorHandler] Error processed: ${context.id} - Handled: ${result.handled}`);
    } catch (handlingError) {
      console.error(`[ErrorHandler] Error processing failed: ${context.id}:`, handlingError);
      result.finalError =
        handlingError instanceof Error ? handlingError : new Error(String(handlingError));
    } finally {
      this.processingCount--;
      result.processingTime = Date.now() - startTime;
    }

    return result;
  }

  /**
   * Attempt error recovery
   */
  private async attemptRecovery(error: Error, context: ErrorContext): Promise<boolean> {
    try {
      if (!(error instanceof BotError)) {
        return false;
      }

      return await this.recoveryManager.recover(error, context);
    } catch (recoveryError) {
      console.error(`[ErrorHandler] Recovery failed for error ${context.id}:`, recoveryError);
      return false;
    }
  }

  /**
   * Attempt fallback
   */
  private async attemptFallback(error: Error, context: ErrorContext): Promise<boolean> {
    try {
      const fallbackType = await this.fallbackService.getFallback(error as BotError, context);
      if (fallbackType) {
        this.emit('error:fallback:used', error, context, fallbackType);
        return true;
      }
      return false;
    } catch (fallbackError) {
      console.error(`[ErrorHandler] Fallback failed for error ${context.id}:`, fallbackError);
      return false;
    }
  }

  /**
   * Send error notifications
   */
  private sendNotifications(
    error: Error,
    context: ErrorContext,
    result: ErrorProcessingResult,
  ): void {
    try {
      const now = Date.now();
      const notificationKey = `${context.phoneNumber || 'admin'}-${error.constructor.name}`;
      const lastNotification = this.lastNotificationTime.get(notificationKey) || 0;

      // Check rate limit
      if (now - lastNotification < this.config.notifications.rateLimitMs) {
        return;
      }

      // Send user notification
      if (this.config.notifications.notifyUsers && context.phoneNumber && !result.handled) {
        this.sendUserNotification(error, context);
        result.userNotified = true;
        this.lastNotificationTime.set(notificationKey, now);
      }

      // Send admin notification for critical errors
      if (this.config.notifications.notifyAdmins && ErrorUtils.getSeverity(error) === 'critical') {
        this.sendAdminNotification(error, context, result);
        result.adminNotified = true;
      }
    } catch (notificationError) {
      console.error(
        `[ErrorHandler] Notification failed for error ${context.id}:`,
        notificationError,
      );
    }
  }

  /**
   * Send user notification
   */
  private sendUserNotification(error: Error, context: ErrorContext): void {
    try {
      const userMessage = ErrorUtils.getUserMessage(error);

      // This would integrate with the messaging service
      console.log(`[ErrorHandler] User notification: ${context.phoneNumber} - ${userMessage}`);

      this.emit('error:notification:sent', error, context, context.phoneNumber || 'user');
    } catch (error) {
      console.error('[ErrorHandler] Failed to send user notification:', error);
    }
  }

  /**
   * Send admin notification
   */
  private sendAdminNotification(
    error: Error,
    context: ErrorContext,
    result: ErrorProcessingResult,
  ): void {
    try {
      const adminMessage = {
        error: error.message,
        context,
        result,
        timestamp: new Date().toISOString(),
      };

      // This would integrate with admin notification system
      console.error(`[ErrorHandler] Admin notification:`, adminMessage);

      this.emit('error:notification:sent', error, context, 'admin');
    } catch (error) {
      console.error('[ErrorHandler] Failed to send admin notification:', error);
    }
  }

  /**
   * Queue error for processing
   */
  private async queueError(error: Error, context: ErrorContext): Promise<ErrorProcessingResult> {
    return new Promise(resolve => {
      this.processingQueue.push({ error, context, resolve });

      // Process queue when capacity is available
      setImmediate(() => {
        this.processQueue().catch(console.error);
      });
    });
  }

  /**
   * Process queued errors
   */
  private async processQueue(): Promise<void> {
    while (
      this.processingQueue.length > 0 &&
      this.processingCount < this.config.maxConcurrentErrors
    ) {
      const { error, context, resolve } = this.processingQueue.shift()!;

      try {
        const result = await this.processError(error, context);
        resolve(result);
      } catch (error) {
        resolve({
          id: context.id,
          handled: false,
          recoveryAttempted: false,
          recoverySuccessful: false,
          fallbackUsed: false,
          userNotified: false,
          adminNotified: false,
          category: 'unknown',
          severity: 'high',
          processingTime: 0,
          finalError: error instanceof Error ? error : new Error(String(error)),
        });
      }
    }
  }

  /**
   * Update error statistics
   */
  private updateStatistics(result: ErrorProcessingResult): void {
    this.errorStats.totalErrors++;

    // Update category stats
    this.errorStats.errorsByCategory[result.category] =
      (this.errorStats.errorsByCategory[result.category] || 0) + 1;

    // Update severity stats
    this.errorStats.errorsBySeverity[result.severity] =
      (this.errorStats.errorsBySeverity[result.severity] || 0) + 1;

    // Update recovery and fallback rates
    const totalRecoveryAttempts = Array.from(this.processingResults.values()).filter(
      r => r.recoveryAttempted,
    ).length;
    const successfulRecoveries = Array.from(this.processingResults.values()).filter(
      r => r.recoverySuccessful,
    ).length;
    this.errorStats.recoverySuccessRate =
      totalRecoveryAttempts > 0 ? successfulRecoveries / totalRecoveryAttempts : 0;

    const fallbackUsages = Array.from(this.processingResults.values()).filter(
      r => r.fallbackUsed,
    ).length;
    this.errorStats.fallbackUsageRate =
      this.errorStats.totalErrors > 0 ? fallbackUsages / this.errorStats.totalErrors : 0;

    // Update average processing time
    const totalProcessingTime = Array.from(this.processingResults.values()).reduce(
      (sum, r) => sum + r.processingTime,
      0,
    );
    this.errorStats.averageProcessingTime =
      this.errorStats.totalErrors > 0 ? totalProcessingTime / this.errorStats.totalErrors : 0;

    // Add to recent errors (keep last 100)
    this.errorStats.recentErrors.push(result);
    if (this.errorStats.recentErrors.length > 100) {
      this.errorStats.recentErrors.shift();
    }

    // Check thresholds
    this.checkThresholds();
  }

  /**
   * Check error thresholds
   */
  private checkThresholds(): void {
    const { alertThresholds } = this.config.monitoring;

    // Check error rate
    const recentErrors = this.errorStats.recentErrors.filter(
      r => Date.now() - new Date(r.id).getTime() < 60000, // Last minute
    );

    this.errorStats.errorRatePerMinute = recentErrors.length;

    if (this.errorStats.errorRatePerMinute > alertThresholds.errorRate) {
      this.emit(
        'error:threshold:exceeded',
        'error_rate',
        this.errorStats.errorRatePerMinute,
        alertThresholds.errorRate,
      );
    }

    // Check critical error rate
    const criticalErrors = recentErrors.filter(r => r.severity === 'critical');
    if (criticalErrors.length > alertThresholds.criticalErrorRate) {
      this.emit(
        'error:threshold:exceeded',
        'critical_error_rate',
        criticalErrors.length,
        alertThresholds.criticalErrorRate,
      );
    }

    // Check recovery failure rate
    const recoveryFailures = recentErrors.filter(r => r.recoveryAttempted && !r.recoverySuccessful);
    if (recoveryFailures.length > alertThresholds.recoveryFailureRate) {
      this.emit(
        'error:threshold:exceeded',
        'recovery_failure_rate',
        recoveryFailures.length,
        alertThresholds.recoveryFailureRate,
      );
    }
  }

  /**
   * Collect metrics
   */
  private collectMetrics(): void {
    this.emit('metrics:collected', { ...this.errorStats });
  }

  /**
   * Get error statistics
   */
  getStatistics(): ErrorStatistics {
    return { ...this.errorStats };
  }

  /**
   * Get processing result
   */
  getProcessingResult(id: string): ErrorProcessingResult | undefined {
    return this.processingResults.get(id);
  }

  /**
   * Clear old processing results
   */
  clearOldResults(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();
    for (const [id, result] of this.processingResults.entries()) {
      if (now - result.processingTime > maxAge) {
        this.processingResults.delete(id);
      }
    }
  }

  /**
   * Shutdown error handler
   */
  async shutdown(): Promise<void> {
    console.log('[ErrorHandler] Shutting down...');

    // Clear intervals
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    // Process remaining queue
    while (this.processingQueue.length > 0) {
      const { context, resolve } = this.processingQueue.shift()!;
      resolve({
        id: context.id,
        handled: false,
        recoveryAttempted: false,
        recoverySuccessful: false,
        fallbackUsed: false,
        userNotified: false,
        adminNotified: false,
        category: 'unknown',
        severity: 'medium',
        processingTime: 0,
        finalError: new Error('System shutting down'),
      });
    }

    // Shutdown components
    this.circuitBreaker.shutdown();
    this.retryManager.shutdown();
    this.recoveryManager.shutdown();
    await this.fallbackService.shutdown();

    console.log('[ErrorHandler] Shutdown complete');
  }
}

/**
 * Default error handler configuration
 */
export const DEFAULT_ERROR_HANDLER_CONFIG: ErrorHandlerConfig = {
  maxConcurrentErrors: 10,
  errorRateThreshold: 5,
  recoveryTimeout: 30000,
  enableAutoRecovery: true,
  enableFallbacks: true,
  notifications: {
    notifyUsers: true,
    notifyAdmins: true,
    rateLimitMs: 60000,
  },
  monitoring: {
    enableMetrics: true,
    metricsIntervalMs: 60000,
    alertThresholds: {
      errorRate: 10,
      criticalErrorRate: 3,
      recoveryFailureRate: 5,
    },
  },
};

/**
 * Create error handler service
 */
export function createErrorHandler(
  config: ErrorHandlerConfig = DEFAULT_ERROR_HANDLER_CONFIG,
): ErrorHandlerService {
  return new ErrorHandlerService(config);
}
