/**
 * Circuit Breaker Implementation
 * Prevents cascading failures by failing fast when services are unavailable
 */

import { EventEmitter } from 'events';

/**
 * Circuit breaker state
 */
export type CircuitState = 'closed' | 'open' | 'half-open';

/**
 * Circuit breaker configuration
 */
export interface CircuitBreakerConfig {
  /** Number of failures before opening circuit */
  failureThreshold: number;
  /** Time to wait before trying to close circuit (ms) */
  recoveryTimeout: number;
  /** Interval for monitoring and cleanup (ms) */
  monitoringInterval: number;
  /** Minimum number of requests before calculating failure rate */
  minimumRequestThreshold: number;
  /** Time window for calculating failure rate (ms) */
  failureRateWindow: number;
  /** Success threshold for closing circuit from half-open */
  successThreshold: number;
}

/**
 * Circuit breaker statistics
 */
export interface CircuitBreakerStats {
  /** Current state */
  state: CircuitState;
  /** Total requests */
  totalRequests: number;
  /** Failed requests */
  failedRequests: number;
  /** Success requests */
  successRequests: number;
  /** Current failure rate */
  failureRate: number;
  /** Last failure time */
  lastFailureTime?: Date;
  /** Time until circuit can be half-opened */
  timeToHalfOpen?: number;
  /** Circuit opened count */
  openedCount: number;
  /** Circuit closed count */
  closedCount: number;
}

/**
 * Service circuit state
 */
interface ServiceCircuitState {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  openedAt?: Date;
  halfOpenedAt?: Date;
  stats: CircuitBreakerStats;
  recentRequests: Array<{ success: boolean; timestamp: Date }>;
}

/**
 * Circuit breaker events
 */
export interface CircuitBreakerEvents {
  'circuit:opened': (service: string, failureRate: number) => void;
  'circuit:closed': (service: string) => void;
  'circuit:half-opened': (service: string) => void;
  'request:rejected': (service: string, reason: string) => void;
  'stats:updated': (service: string, stats: CircuitBreakerStats) => void;
}



/**
 * Circuit Breaker Implementation
 */
export class CircuitBreaker extends EventEmitter {
  private config: CircuitBreakerConfig;
  private circuits: Map<string, ServiceCircuitState> = new Map();
  private monitoringInterval?: NodeJS.Timeout;

  constructor(config: CircuitBreakerConfig) {
    super();
    this.config = config;

    // Start monitoring
    this.monitoringInterval = setInterval(() => {
      this.monitorCircuits();
    }, config.monitoringInterval);

    console.log('[CircuitBreaker] Initialized with configuration:', config);
  }

  /**
   * Execute operation with circuit breaker protection
   */
  async execute<T>(service: string, operation: () => Promise<T>): Promise<T> {
    const circuit = this.getOrCreateCircuit(service);

    // Check if circuit is open
    if (circuit.state === 'open') {
      if (this.shouldAttemptHalfOpen(circuit)) {
        this.setCircuitState(service, 'half-open');
      } else {
        this.emit('request:rejected', service, 'Circuit is open');
        throw new Error(`Circuit breaker is open for service: ${service}`);
      }
    }

    try {
      const result = await operation();
      this.recordSuccess(service);
      return result;
    } catch (error) {
      this.recordFailure(service);
      throw error;
    }
  }

  /**
   * Record successful operation
   */
  recordSuccess(service: string): void {
    const circuit = this.getOrCreateCircuit(service);

    circuit.successCount++;
    circuit.lastSuccessTime = new Date();

    // Add to recent requests
    this.addRecentRequest(circuit, true);

    // Update stats
    this.updateStats(service, circuit);

    // Check if circuit should be closed
    if (circuit.state === 'half-open') {
      if (circuit.successCount >= this.config.successThreshold) {
        this.setCircuitState(service, 'closed');
        circuit.failureCount = 0;
      }
    } else if (circuit.state === 'open') {
      // Reset failure count on first success after opening
      circuit.failureCount = 0;
    }

    console.log(`[CircuitBreaker] Success recorded for service: ${service} (${circuit.state})`);
  }

  /**
   * Record failed operation
   */
  recordFailure(service: string): void {
    const circuit = this.getOrCreateCircuit(service);

    circuit.failureCount++;
    circuit.lastFailureTime = new Date();

    // Add to recent requests
    this.addRecentRequest(circuit, false);

    // Update stats
    this.updateStats(service, circuit);

    // Check if circuit should be opened
    if (circuit.state === 'closed' || circuit.state === 'half-open') {
      if (this.shouldOpenCircuit(circuit)) {
        this.setCircuitState(service, 'open');
      }
    }

    console.log(
      `[CircuitBreaker] Failure recorded for service: ${service} (${circuit.state}) - Failures: ${circuit.failureCount}`,
    );
  }

  /**
   * Check if circuit is open
   */
  isOpen(service: string): boolean {
    const circuit = this.circuits.get(service);
    return circuit ? circuit.state === 'open' : false;
  }

  /**
   * Check if circuit is closed
   */
  isClosed(service: string): boolean {
    const circuit = this.circuits.get(service);
    return circuit ? circuit.state === 'closed' : true;
  }

  /**
   * Check if circuit is half-open
   */
  isHalfOpen(service: string): boolean {
    const circuit = this.circuits.get(service);
    return circuit ? circuit.state === 'half-open' : false;
  }

  /**
   * Get circuit state
   */
  getState(service: string): CircuitState {
    const circuit = this.circuits.get(service);
    return circuit ? circuit.state : 'closed';
  }

  /**
   * Get circuit statistics
   */
  getStats(service: string): CircuitBreakerStats | undefined {
    const circuit = this.circuits.get(service);
    return circuit ? { ...circuit.stats } : undefined;
  }

  /**
   * Get all circuit statistics
   */
  getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    for (const [service, circuit] of this.circuits.entries()) {
      stats[service] = { ...circuit.stats };
    }
    return stats;
  }

  /**
   * Reset circuit for service
   */
  reset(service: string): void {
    const circuit = this.circuits.get(service);
    if (circuit) {
      circuit.state = 'closed';
      circuit.failureCount = 0;
      circuit.successCount = 0;
      circuit.lastFailureTime = undefined;
      circuit.lastSuccessTime = undefined;
      circuit.openedAt = undefined;
      circuit.halfOpenedAt = undefined;
      circuit.recentRequests = [];

      this.updateStats(service, circuit);

      console.log(`[CircuitBreaker] Reset circuit for service: ${service}`);
    }
  }

  /**
   * Reset all circuits
   */
  resetAll(): void {
    for (const service of this.circuits.keys()) {
      this.reset(service);
    }
  }

  /**
   * Get or create circuit for service
   */
  private getOrCreateCircuit(service: string): ServiceCircuitState {
    if (!this.circuits.has(service)) {
      const circuit: ServiceCircuitState = {
        state: 'closed',
        failureCount: 0,
        successCount: 0,
        stats: {
          state: 'closed',
          totalRequests: 0,
          failedRequests: 0,
          successRequests: 0,
          failureRate: 0,
          openedCount: 0,
          closedCount: 0,
        },
        recentRequests: [],
      };
      this.circuits.set(service, circuit);
    }
    return this.circuits.get(service)!;
  }

  /**
   * Set circuit state
   */
  private setCircuitState(service: string, state: CircuitState): void {
    const circuit = this.getOrCreateCircuit(service);
    const previousState = circuit.state;
    circuit.state = state;

    const now = new Date();

    switch (state) {
      case 'open':
        circuit.openedAt = now;
        circuit.stats.openedCount++;
        this.emit('circuit:opened', service, circuit.stats.failureRate);
        break;
      case 'half-open':
        circuit.halfOpenedAt = now;
        circuit.successCount = 0; // Reset success count for half-open evaluation
        this.emit('circuit:half-opened', service);
        break;
      case 'closed':
        circuit.openedAt = undefined;
        circuit.halfOpenedAt = undefined;
        circuit.stats.closedCount++;
        this.emit('circuit:closed', service);
        break;
    }

    this.updateStats(service, circuit);

    console.log(
      `[CircuitBreaker] Circuit state changed for ${service}: ${previousState} -> ${state}`,
    );
  }

  /**
   * Check if circuit should be opened
   */
  private shouldOpenCircuit(circuit: ServiceCircuitState): boolean {
    // Check minimum request threshold
    if (circuit.stats.totalRequests < this.config.minimumRequestThreshold) {
      return false;
    }

    // Check failure rate
    const failureRate = this.calculateFailureRate(circuit);
    return failureRate >= this.config.failureThreshold;
  }

  /**
   * Check if circuit should attempt half-open
   */
  private shouldAttemptHalfOpen(circuit: ServiceCircuitState): boolean {
    if (!circuit.openedAt) {
      return false;
    }

    const timeSinceOpened = Date.now() - circuit.openedAt.getTime();
    return timeSinceOpened >= this.config.recoveryTimeout;
  }

  /**
   * Calculate failure rate
   */
  private calculateFailureRate(circuit: ServiceCircuitState): number {
    const now = Date.now();
    const windowStart = now - this.config.failureRateWindow;

    const recentRequests = circuit.recentRequests.filter(
      req => req.timestamp.getTime() >= windowStart,
    );

    if (recentRequests.length === 0) {
      return 0;
    }

    const failures = recentRequests.filter(req => !req.success).length;
    return failures / recentRequests.length;
  }

  /**
   * Add recent request
   */
  private addRecentRequest(circuit: ServiceCircuitState, success: boolean): void {
    const now = new Date();
    circuit.recentRequests.push({ success, timestamp: now });

    // Keep only requests within the window
    const windowStart = now.getTime() - this.config.failureRateWindow;
    circuit.recentRequests = circuit.recentRequests.filter(
      req => req.timestamp.getTime() >= windowStart,
    );
  }

  /**
   * Update circuit statistics
   */
  private updateStats(service: string, circuit: ServiceCircuitState): void {
    const stats = circuit.stats;

    stats.state = circuit.state;
    stats.totalRequests = circuit.successCount + circuit.failureCount;
    stats.failedRequests = circuit.failureCount;
    stats.successRequests = circuit.successCount;
    stats.failureRate = this.calculateFailureRate(circuit);
    stats.lastFailureTime = circuit.lastFailureTime;

    // Calculate time to half-open
    if (circuit.state === 'open' && circuit.openedAt) {
      const timeSinceOpened = Date.now() - circuit.openedAt.getTime();
      stats.timeToHalfOpen = Math.max(0, this.config.recoveryTimeout - timeSinceOpened);
    } else {
      stats.timeToHalfOpen = undefined;
    }

    this.emit('stats:updated', service, { ...stats });
  }

  /**
   * Monitor circuits
   */
  private monitorCircuits(): void {
    const now = Date.now();

    for (const [service, circuit] of this.circuits.entries()) {
      // Clean up old requests
      const windowStart = now - this.config.failureRateWindow;
      circuit.recentRequests = circuit.recentRequests.filter(
        req => req.timestamp.getTime() >= windowStart,
      );

      // Update stats
      this.updateStats(service, circuit);

      // Check if open circuit should be half-opened
      if (circuit.state === 'open' && this.shouldAttemptHalfOpen(circuit)) {
        console.log(`[CircuitBreaker] Auto-transitioning to half-open for service: ${service}`);
        this.setCircuitState(service, 'half-open');
      }
    }
  }

  /**
   * Get circuit breaker configuration
   */
  getConfig(): CircuitBreakerConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<CircuitBreakerConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('[CircuitBreaker] Configuration updated:', this.config);
  }

  /**
   * Shutdown circuit breaker
   */
  async shutdown(): Promise<void> {
    console.log('[CircuitBreaker] Shutting down...');

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.circuits.clear();

    console.log('[CircuitBreaker] Shutdown complete');
  }
}

/**
 * Default circuit breaker configuration
 */
export const DEFAULT_CIRCUIT_BREAKER_CONFIG: CircuitBreakerConfig = {
  failureThreshold: 0.5, // 50% failure rate
  recoveryTimeout: 30000, // 30 seconds
  monitoringInterval: 5000, // 5 seconds
  minimumRequestThreshold: 5, // Minimum 5 requests before calculating failure rate
  failureRateWindow: 60000, // 1 minute window
  successThreshold: 3, // 3 successful requests to close from half-open
};

/**
 * Create circuit breaker instance
 */
export function createCircuitBreaker(
  config: CircuitBreakerConfig = DEFAULT_CIRCUIT_BREAKER_CONFIG,
): CircuitBreaker {
  return new CircuitBreaker(config);
}
